@page "/forgot-password-confirmation"
@using MudBlazor

<PageTitle>E-mail Enviado - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudPaper Elevation="4" Class="pa-8">
        <MudStack Spacing="4" AlignItems="AlignItems.Center">
            <!-- Ícone de sucesso -->
            <MudIcon Icon="@Icons.Material.Filled.MarkEmailRead" 
                     Size="Size.Large" 
                     Color="Color.Success" 
                     Style="font-size: 4rem;" />

            <!-- Título -->
            <MudText Typo="Typo.h4" Align="Align.Center" Color="Color.Primary">
                E-mail Enviado!
            </MudText>

            <!-- Mensagem principal -->
            <MudAlert Severity="Severity.Success" Variant="Variant.Filled" Class="ma-0">
                <MudText Typo="Typo.body1">
                    Se o e-mail informado estiver cadastrado em nosso sistema, você receberá um link para redefinir sua senha.
                </MudText>
            </MudAlert>

            <!-- Instruções -->
            <MudStack Spacing="2" Class="text-center">
                <MudText Typo="Typo.h6" Color="Color.Primary">
                    Próximos passos:
                </MudText>
                
                <MudList T="string">
                    <MudListItem Icon="@Icons.Material.Filled.Email" IconColor="Color.Primary">
                        <MudText>Verifique sua caixa de entrada</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Folder" IconColor="Color.Warning">
                        <MudText>Confira também a pasta de spam/lixo eletrônico</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Link" IconColor="Color.Info">
                        <MudText>Clique no link recebido para redefinir sua senha</MudText>
                    </MudListItem>
                    <MudListItem Icon="@Icons.Material.Filled.Schedule" IconColor="Color.Secondary">
                        <MudText>O link expira em 24 horas</MudText>
                    </MudListItem>
                </MudList>
            </MudStack>

            <!-- Informações adicionais -->
            <MudAlert Severity="Severity.Info" Variant="Variant.Text">
                <MudText Typo="Typo.body2">
                    <strong>Não recebeu o e-mail?</strong><br/>
                    Aguarde alguns minutos e verifique sua conexão com a internet. 
                    Se ainda assim não receber, você pode solicitar um novo link.
                </MudText>
            </MudAlert>

            <!-- Ações -->
            <MudStack Row="true" Spacing="2" Justify="Justify.Center" Class="mt-4">
                <MudButton Href="/forgot-password"
                           Variant="Variant.Outlined"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.Refresh">
                    Enviar Novamente
                </MudButton>
                
                <MudButton Href="/login"
                           Variant="Variant.Filled"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.Login">
                    Voltar ao Login
                </MudButton>
            </MudStack>

            <!-- Suporte -->
            <MudDivider Class="my-4" />
            
            <MudStack AlignItems="AlignItems.Center" Spacing="1">
                <MudText Typo="Typo.caption" Color="Color.Secondary">
                    Precisa de ajuda?
                </MudText>
                <MudButton Variant="Variant.Text" 
                           Color="Color.Secondary" 
                           Size="Size.Small"
                           StartIcon="@Icons.Material.Filled.Support">
                    Contatar Suporte
                </MudButton>
            </MudStack>
        </MudStack>
    </MudPaper>
</MudContainer>

@code {
    // Esta página é estática, não precisa de código adicional
}
