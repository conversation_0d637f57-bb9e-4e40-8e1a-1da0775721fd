namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Interface para serviço de auditoria
/// </summary>
public interface IAuditService
{
    /// <summary>
    /// Registra evento de solicitação de recuperação de senha
    /// </summary>
    /// <param name="email">E-mail do usuário</param>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="userAgent">User Agent</param>
    /// <param name="success">Se a operação foi bem-sucedida</param>
    /// <param name="reason">Motivo em caso de falha</param>
    Task LogPasswordResetRequestAsync(string email, string? ipAddress, string? userAgent, bool success, string? reason = null);

    /// <summary>
    /// Registra evento de reset de senha
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <param name="email">E-mail do usuário</param>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="userAgent">User Agent</param>
    /// <param name="success">Se a operação foi bem-sucedida</param>
    /// <param name="reason">Motivo em caso de falha</param>
    Task LogPasswordResetAsync(string userId, string email, string? ipAddress, string? userAgent, bool success, string? reason = null);

    /// <summary>
    /// Registra evento de login
    /// </summary>
    /// <param name="emailOrUserName">E-mail ou nome de usuário</param>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="userAgent">User Agent</param>
    /// <param name="success">Se a operação foi bem-sucedida</param>
    /// <param name="reason">Motivo em caso de falha</param>
    Task LogLoginAttemptAsync(string emailOrUserName, string? ipAddress, string? userAgent, bool success, string? reason = null);

    /// <summary>
    /// Registra evento de validação de token
    /// </summary>
    /// <param name="tokenHash">Hash do token (para não expor o token real)</param>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="userAgent">User Agent</param>
    /// <param name="success">Se a validação foi bem-sucedida</param>
    /// <param name="reason">Motivo em caso de falha</param>
    Task LogTokenValidationAsync(string tokenHash, string? ipAddress, string? userAgent, bool success, string? reason = null);

    /// <summary>
    /// Registra evento de rate limiting
    /// </summary>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="path">Caminho da requisição</param>
    /// <param name="userAgent">User Agent</param>
    Task LogRateLimitExceededAsync(string ipAddress, string path, string? userAgent);
}
