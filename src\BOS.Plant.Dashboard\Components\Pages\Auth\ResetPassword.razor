@page "/reset-password"
@layout AuthLayout
@using BOS.Plant.Modules.Auth.Services
@using MudBlazor
@inject IAuthService AuthService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Redefinir Senha - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudPaper Elevation="4" Class="pa-8">
        <MudStack Spacing="4">
            <!-- Header -->
            <MudStack AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@(_tokenValid ? Icons.Material.Filled.VpnKey : Icons.Material.Filled.Error)" 
                         Size="Size.Large" 
                         Color="@(_tokenValid ? Color.Primary : Color.Error)" />
                <MudText Typo="Typo.h4" Align="Align.Center" Color="@(_tokenValid ? Color.Primary : Color.Error)">
                    @(_tokenValid ? "Redefinir Senha" : "Link Inválido")
                </MudText>
                <MudText Typo="Typo.body1" Align="Align.Center" Color="Color.Secondary">
                    @(_tokenValid ? "Digite sua nova senha" : "Este link de recuperação é inválido ou expirou")
                </MudText>
            </MudStack>

            @if (_isValidating)
            {
                <!-- Loading durante validação -->
                <MudStack AlignItems="AlignItems.Center" Spacing="2">
                    <MudProgressCircular Indeterminate="true" />
                    <MudText>Validando link de recuperação...</MudText>
                </MudStack>
            }
            else if (_tokenValid)
            {
                <!-- Formulário de reset -->
                <EditForm Model="@_model" OnValidSubmit="@HandleSubmit">
                    <DataAnnotationsValidator />
                    
                    <MudStack Spacing="3">
                        <MudTextField @bind-Value="_model.Email"
                                      Label="E-mail"
                                      Variant="Variant.Outlined"
                                      InputType="InputType.Email"
                                      Adornment="Adornment.Start"
                                      AdornmentIcon="@Icons.Material.Filled.Email"
                                      Required="true"
                                      RequiredError="E-mail é obrigatório"
                                      For="@(() => _model.Email)"
                                      Disabled="@_isLoading" />

                        <MudTextField @bind-Value="_model.NewPassword"
                                      @oninput="@((ChangeEventArgs e) => OnPasswordChanged(e.Value?.ToString() ?? ""))"
                                      Label="Nova Senha"
                                      Variant="Variant.Outlined"
                                      InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@(_showPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                      OnAdornmentClick="@(() => _showPassword = !_showPassword)"
                                      Required="true"
                                      RequiredError="Nova senha é obrigatória"
                                      For="@(() => _model.NewPassword)"
                                      Disabled="@_isLoading" />

                        <MudTextField @bind-Value="_model.ConfirmPassword"
                                      Label="Confirmar Nova Senha"
                                      Variant="Variant.Outlined"
                                      InputType="@(_showConfirmPassword ? InputType.Text : InputType.Password)"
                                      Adornment="Adornment.End"
                                      AdornmentIcon="@(_showConfirmPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                      OnAdornmentClick="@(() => _showConfirmPassword = !_showConfirmPassword)"
                                      Required="true"
                                      RequiredError="Confirmação de senha é obrigatória"
                                      For="@(() => _model.ConfirmPassword)"
                                      Disabled="@_isLoading" />

                        <!-- Indicador de força da senha -->
                        @if (!string.IsNullOrEmpty(_model.NewPassword))
                        {
                            <MudStack Spacing="1">
                                <MudText Typo="Typo.caption">Força da senha:</MudText>
                                <MudProgressLinear Value="@_passwordStrength" 
                                                   Color="@GetPasswordStrengthColor()" 
                                                   Size="Size.Small" />
                                <MudText Typo="Typo.caption" Color="@GetPasswordStrengthColor()">
                                    @GetPasswordStrengthText()
                                </MudText>
                            </MudStack>
                        }

                        <MudButton ButtonType="ButtonType.Submit"
                                   Variant="Variant.Filled"
                                   Color="Color.Primary"
                                   Size="Size.Large"
                                   FullWidth="true"
                                   StartIcon="@Icons.Material.Filled.Save"
                                   loading="@_isLoading"
                                   loading-text="Salvando..."
                                   Disabled="@(_passwordStrength < 60)">
                            Redefinir Senha
                        </MudButton>
                    </MudStack>
                </EditForm>
            }
            else
            {
                <!-- Token inválido -->
                <MudStack AlignItems="AlignItems.Center" Spacing="3">
                    <MudAlert Severity="Severity.Error" Variant="Variant.Filled">
                        O link de recuperação é inválido, expirou ou já foi usado.
                    </MudAlert>
                    
                    <MudButton Href="/forgot-password"
                               Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Refresh">
                        Solicitar Novo Link
                    </MudButton>
                </MudStack>
            }

            <!-- Links de navegação -->
            <MudDivider />
            
            <MudStack Row="true" Justify="Justify.Center">
                <MudButton Href="/login"
                           Variant="Variant.Text"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.ArrowBack">
                    Voltar ao Login
                </MudButton>
            </MudStack>
        </MudStack>
    </MudPaper>
</MudContainer>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? Token { get; set; }

    private readonly ResetPasswordModel _model = new();
    private bool _isLoading = false;
    private bool _isValidating = true;
    private bool _tokenValid = false;
    private bool _showPassword = false;
    private bool _showConfirmPassword = false;
    private double _passwordStrength = 0.0;


    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrEmpty(Token))
        {
            _isValidating = false;
            _tokenValid = false;
            return;
        }

        try
        {
            var result = await AuthService.ValidateResetTokenAsync(Token);
            _tokenValid = result.Success;

            if (_tokenValid && result.User != null)
            {
                _model.Email = result.User.Email;
            }
        }
        catch
        {
            _tokenValid = false;
        }
        finally
        {
            _isValidating = false;
            StateHasChanged();
        }
    }

    protected override void OnParametersSet()
    {
        if (!string.IsNullOrEmpty(_model.NewPassword))
        {
            _passwordStrength = CalculatePasswordStrength(_model.NewPassword);
        }
    }

    private void OnPasswordChanged(string value)
    {
        _model.NewPassword = value;
        _passwordStrength = CalculatePasswordStrength(value);
        StateHasChanged();
    }

    private async Task HandleSubmit()
    {
        if (_isLoading || string.IsNullOrEmpty(Token)) return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            var request = new ResetPasswordRequest
            {
                Email = _model.Email,
                Token = Token,
                NewPassword = _model.NewPassword
            };

            var result = await AuthService.ResetPasswordAsync(request);

            if (result.Success)
            {
                Snackbar.Add("Senha redefinida com sucesso!", Severity.Success);
                Navigation.NavigateTo("/login");
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao redefinir senha", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Erro interno. Tente novamente mais tarde.", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private double CalculatePasswordStrength(string password)
    {
        if (string.IsNullOrEmpty(password)) return 0;

        double strength = 0;

        // Comprimento
        if (password.Length >= 8) strength += 25;
        if (password.Length >= 12) strength += 15;

        // Caracteres
        if (password.Any(char.IsLower)) strength += 15;
        if (password.Any(char.IsUpper)) strength += 15;
        if (password.Any(char.IsDigit)) strength += 15;
        if (password.Any(c => !char.IsLetterOrDigit(c))) strength += 15;

        return Math.Min(100, strength);
    }

    private Color GetPasswordStrengthColor()
    {
        return _passwordStrength switch
        {
            (< 40) => Color.Error,
            (< 70) => Color.Warning,
            _ => Color.Success
        };
    }

    private string GetPasswordStrengthText()
    {
        return _passwordStrength switch
        {
            (< 40) => "Fraca",
            (< 70) => "Média",
            _ => "Forte"
        };
    }

    public class ResetPasswordModel
    {
        [Required(ErrorMessage = "E-mail é obrigatório")]
        [EmailAddress(ErrorMessage = "E-mail inválido")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nova senha é obrigatória")]
        [MinLength(8, ErrorMessage = "A senha deve ter pelo menos 8 caracteres")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirmação de senha é obrigatória")]
        [Compare(nameof(NewPassword), ErrorMessage = "As senhas não coincidem")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
