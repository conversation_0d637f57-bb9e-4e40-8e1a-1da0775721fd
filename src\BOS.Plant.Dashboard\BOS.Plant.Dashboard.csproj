<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Extensions.MudBlazor.StaticInput" Version="3.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Microsoft.Net.Http.Headers" Version="9.0.6" />
    <PackageReference Include="MudBlazor" Version="8.9.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
  </ItemGroup>


  <ItemGroup>
    <ProjectReference Include="..\Application\BOS.Plant.Application.csproj" />
    <ProjectReference Include="..\Core\BOS.Plant.Core.csproj" />
    <ProjectReference Include="..\Infrastructure\BOS.Plant.Infrastructure.csproj" />
    <ProjectReference Include="..\Modules\Auth\BOS.Plant.Modules.Auth.csproj" />
    <ProjectReference Include="..\Modules\OpcDa\BOS.Plant.Modules.OpcDa.csproj" />
    <ProjectReference Include="..\Modules\Projects\BOS.Plant.Modules.Projects.csproj" />
    <ProjectReference Include="..\Modules\Tenants\BOS.Plant.Modules.Tenants.csproj" />
    <ProjectReference Include="..\Modules\Users\BOS.Plant.Modules.Users.csproj" />
    <ProjectReference Include="..\Shared\BOS.Plant.Shared.csproj" />
  </ItemGroup>
</Project>