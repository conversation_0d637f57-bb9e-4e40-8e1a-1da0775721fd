using BOS.Plant.Core.Entities;
using BOS.Plant.Core.Interfaces;
using BOS.Plant.Shared.Models;
using Microsoft.Extensions.Logging;

namespace BOS.Plant.Modules.Tenants.Services;

/// <summary>
/// Implementação do serviço de gestão de tenant
/// </summary>
public class TenantManagementService : ITenantManagementService
{
    private readonly IRepository<Tenant> _tenantRepository;
    private readonly ITenantRepository<ApplicationUser> _userRepository;
    private readonly ITenantRepository<Project> _projectRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly ILogger<TenantManagementService> _logger;

    public TenantManagementService(
        IRepository<Tenant> tenantRepository,
        ITenantRepository<ApplicationUser> userRepository,
        ITenantRepository<Project> projectRepository,
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        ILogger<TenantManagementService> logger)
    {
        _tenantRepository = tenantRepository;
        _userRepository = userRepository;
        _projectRepository = projectRepository;
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<Tenant?> GetCurrentTenantAsync()
    {
        return await _tenantService.GetCurrentTenantAsync();
    }

    public async Task<TenantResult> UpdateTenantSettingsAsync(UpdateTenantSettingsRequest request)
    {
        try
        {
            var tenant = await GetCurrentTenantAsync();
            if (tenant == null)
            {
                return new TenantResult
                {
                    Success = false,
                    Message = "Tenant não encontrado"
                };
            }

            tenant.Name = request.Name;
            tenant.Settings = request.Settings;

            await _tenantRepository.UpdateAsync(tenant);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Configurações do tenant {TenantId} atualizadas", tenant.Id);

            return new TenantResult
            {
                Success = true,
                Message = "Configurações atualizadas com sucesso",
                Tenant = tenant
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar configurações do tenant");
            return new TenantResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<PagedResult<ApplicationUser>> GetTenantUsersAsync(int page, int pageSize, string? searchTerm = null)
    {
        try
        {
            var (users, totalCount) = await _userRepository.GetPagedAsync(
                page,
                pageSize,
                predicate: string.IsNullOrEmpty(searchTerm) ? null : 
                    u => u.FullName.Contains(searchTerm) || u.Email.Contains(searchTerm) || u.UserName.Contains(searchTerm),
                orderBy: u => u.FullName
            );

            return new PagedResult<ApplicationUser>
            {
                Items = users,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter usuários do tenant");
            return new PagedResult<ApplicationUser>();
        }
    }

    public async Task<TenantStatistics> GetTenantStatisticsAsync()
    {
        try
        {
            var tenant = await GetCurrentTenantAsync();
            var users = await _userRepository.GetAllAsync();
            var projects = await _projectRepository.GetAllAsync();

            var usersList = users.ToList();
            var projectsList = projects.ToList();

            return new TenantStatistics
            {
                TotalUsers = usersList.Count,
                ActiveUsers = usersList.Count(u => u.IsActive),
                TotalProjects = projectsList.Count,
                ActiveProjects = projectsList.Count(p => p.Status == ProjectStatus.InProgress),
                LastUserLogin = usersList.Where(u => u.LastLoginAt.HasValue).Max(u => u.LastLoginAt),
                CreatedAt = tenant?.CreatedAt ?? DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do tenant");
            return new TenantStatistics();
        }
    }

    public async Task<TenantLimits> GetTenantLimitsAsync()
    {
        try
        {
            var tenant = await GetCurrentTenantAsync();
            var users = await _userRepository.GetAllAsync();
            var projects = await _projectRepository.GetAllAsync();

            return new TenantLimits
            {
                MaxUsers = tenant?.MaxUsers,
                MaxProjects = tenant?.MaxProjects,
                CurrentUsers = users.Count(),
                CurrentProjects = projects.Count(),
                SubscriptionExpiresAt = tenant?.SubscriptionExpiresAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter limites do tenant");
            return new TenantLimits();
        }
    }

    public async Task<TenantResult> UpdateTenantLimitsAsync(UpdateTenantLimitsRequest request)
    {
        try
        {
            var tenant = await GetCurrentTenantAsync();
            if (tenant == null)
            {
                return new TenantResult
                {
                    Success = false,
                    Message = "Tenant não encontrado"
                };
            }

            tenant.MaxUsers = request.MaxUsers;
            tenant.MaxProjects = request.MaxProjects;
            tenant.SubscriptionExpiresAt = request.SubscriptionExpiresAt;

            await _tenantRepository.UpdateAsync(tenant);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Limites do tenant {TenantId} atualizados", tenant.Id);

            return new TenantResult
            {
                Success = true,
                Message = "Limites atualizados com sucesso",
                Tenant = tenant
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar limites do tenant");
            return new TenantResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }
}
