namespace BOS.Plant.Core.Entities;

/// <summary>
/// Entidade que representa a relação entre usuário e role
/// </summary>
public class UserRole
{
    /// <summary>
    /// ID do usuário
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Usuário
    /// </summary>
    public ApplicationUser User { get; set; } = null!;

    /// <summary>
    /// ID da role
    /// </summary>
    public string RoleId { get; set; } = string.Empty;

    /// <summary>
    /// Role
    /// </summary>
    public ApplicationRole Role { get; set; } = null!;

    /// <summary>
    /// Data de criação da relação
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID do usuário que criou a relação
    /// </summary>
    public string? CreatedBy { get; set; }
}
