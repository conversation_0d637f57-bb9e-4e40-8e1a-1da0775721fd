namespace BOS.Plant.Modules.OpcDa.Models;

/// <summary>
/// Detalhes completos de um tag OPC obtidos através de browsing real
/// </summary>
public class OpcTagDetails
{
    /// <summary>
    /// ID do item OPC
    /// </summary>
    public string ItemId { get; set; } = string.Empty;

    /// <summary>
    /// Nome do tag
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do tag
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tipo de dados canônico
    /// </summary>
    public string? DataType { get; set; }

    /// <summary>
    /// Valor atual do tag
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Qualidade do valor
    /// </summary>
    public string? Quality { get; set; }

    /// <summary>
    /// Timestamp do valor
    /// </summary>
    public DateTime? Timestamp { get; set; }

    /// <summary>
    /// Direitos de acesso
    /// </summary>
    public string? AccessRights { get; set; }

    /// <summary>
    /// Valor mínimo (se aplicável)
    /// </summary>
    public object? MinValue { get; set; }

    /// <summary>
    /// Valor máximo (se aplicável)
    /// </summary>
    public object? MaxValue { get; set; }

    /// <summary>
    /// Unidade de engenharia
    /// </summary>
    public string? EngineeringUnit { get; set; }

    /// <summary>
    /// Taxa de atualização recomendada (ms)
    /// </summary>
    public int? RecommendedUpdateRate { get; set; }

    /// <summary>
    /// Todas as propriedades OPC do tag
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// Metadados adicionais
    /// </summary>
    public Dictionary<string, string> Metadata { get; set; } = new();

    /// <summary>
    /// Indica se o tag é legível
    /// </summary>
    public bool IsReadable => AccessRights?.Contains("read", StringComparison.OrdinalIgnoreCase) == true;

    /// <summary>
    /// Indica se o tag é gravável
    /// </summary>
    public bool IsWritable => AccessRights?.Contains("write", StringComparison.OrdinalIgnoreCase) == true;

    /// <summary>
    /// Timestamp da obtenção dos detalhes
    /// </summary>
    public DateTime RetrievedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Servidor de origem
    /// </summary>
    public string? SourceServer { get; set; }

    /// <summary>
    /// Host de origem
    /// </summary>
    public string? SourceHost { get; set; }
}

/// <summary>
/// Resultado de uma operação de browsing real
/// </summary>
public class OpcBrowsingResult
{
    /// <summary>
    /// Tags encontrados
    /// </summary>
    public List<OpcTagNode> Tags { get; set; } = new();

    /// <summary>
    /// Detalhes de tags (quando solicitados)
    /// </summary>
    public List<OpcTagDetails> TagDetails { get; set; } = new();

    /// <summary>
    /// Caminho navegado
    /// </summary>
    public string? BrowsedPath { get; set; }

    /// <summary>
    /// Servidor navegado
    /// </summary>
    public string? ServerProgId { get; set; }

    /// <summary>
    /// Host navegado
    /// </summary>
    public string? Host { get; set; }

    /// <summary>
    /// Timestamp do browsing
    /// </summary>
    public DateTime BrowsedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Tempo gasto no browsing
    /// </summary>
    public TimeSpan BrowsingDuration { get; set; }

    /// <summary>
    /// Indica se o browsing foi bem-sucedido
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Número total de elementos encontrados
    /// </summary>
    public int TotalElements => Tags.Count;

    /// <summary>
    /// Número de branches (pastas)
    /// </summary>
    public int BranchCount => Tags.Count(t => t.IsBranch);

    /// <summary>
    /// Número de leaves (tags)
    /// </summary>
    public int LeafCount => Tags.Count(t => t.IsLeaf);
}

/// <summary>
/// Configuração para operações de browsing
/// </summary>
public class OpcBrowsingConfig
{
    /// <summary>
    /// Incluir propriedades detalhadas dos tags
    /// </summary>
    public bool IncludeProperties { get; set; } = false;

    /// <summary>
    /// Incluir valores atuais dos tags
    /// </summary>
    public bool IncludeValues { get; set; } = false;

    /// <summary>
    /// Filtro de nome de elemento
    /// </summary>
    public string? ElementNameFilter { get; set; }

    /// <summary>
    /// Filtro de vendor
    /// </summary>
    public string? VendorFilter { get; set; }

    /// <summary>
    /// Tipo de filtro de browsing
    /// </summary>
    public OpcBrowseFilter BrowseFilter { get; set; } = OpcBrowseFilter.All;

    /// <summary>
    /// Profundidade máxima de browsing recursivo
    /// </summary>
    public int MaxDepth { get; set; } = 10;

    /// <summary>
    /// Número máximo de elementos a retornar
    /// </summary>
    public int MaxElements { get; set; } = 1000;

    /// <summary>
    /// Timeout para operações de browsing
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Usar cache de browsing
    /// </summary>
    public bool UseCache { get; set; } = true;

    /// <summary>
    /// Tempo de vida do cache
    /// </summary>
    public TimeSpan CacheLifetime { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Tipos de filtro de browsing OPC
/// </summary>
public enum OpcBrowseFilter
{
    /// <summary>
    /// Todos os elementos
    /// </summary>
    All,

    /// <summary>
    /// Apenas branches (pastas)
    /// </summary>
    Branches,

    /// <summary>
    /// Apenas items (tags)
    /// </summary>
    Items
}

/// <summary>
/// Estatísticas de browsing
/// </summary>
public class OpcBrowsingStatistics
{
    /// <summary>
    /// Número total de operações de browsing
    /// </summary>
    public long TotalBrowsingOperations { get; set; }

    /// <summary>
    /// Número de operações bem-sucedidas
    /// </summary>
    public long SuccessfulOperations { get; set; }

    /// <summary>
    /// Número de operações com falha
    /// </summary>
    public long FailedOperations { get; set; }

    /// <summary>
    /// Tempo médio de browsing
    /// </summary>
    public TimeSpan AverageBrowsingTime { get; set; }

    /// <summary>
    /// Número total de tags descobertos
    /// </summary>
    public long TotalTagsDiscovered { get; set; }

    /// <summary>
    /// Cache hits
    /// </summary>
    public long CacheHits { get; set; }

    /// <summary>
    /// Cache misses
    /// </summary>
    public long CacheMisses { get; set; }

    /// <summary>
    /// Taxa de sucesso
    /// </summary>
    public double SuccessRate => TotalBrowsingOperations > 0 ? 
        (double)SuccessfulOperations / TotalBrowsingOperations * 100 : 0;

    /// <summary>
    /// Taxa de cache hit
    /// </summary>
    public double CacheHitRate => (CacheHits + CacheMisses) > 0 ? 
        (double)CacheHits / (CacheHits + CacheMisses) * 100 : 0;

    /// <summary>
    /// Última atualização das estatísticas
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}
