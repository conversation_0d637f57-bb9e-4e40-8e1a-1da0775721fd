using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using BOS.Plant.Modules.OpcDa.Interfaces;
using BOS.Plant.Modules.OpcDa.Models;
using System.Collections.Concurrent;

namespace BOS.Plant.Modules.OpcDa.Services;

/// <summary>
/// Serviço para gerenciamento de timeouts configuráveis em operações OPC
/// </summary>
public class OpcTimeoutService : IOpcTimeoutService
{
    private readonly ILogger<OpcTimeoutService> _logger;
    private readonly OpcTimeoutOptions _options;
    private readonly ConcurrentDictionary<string, OpcOperationTimeout> _activeOperations = new();
    private readonly System.Threading.Timer _timeoutCheckTimer;

    public event EventHandler<OpcTimeoutEventArgs>? OperationTimedOut;
    public event EventHandler<OpcTimeoutEventArgs>? OperationCompleted;

    public OpcTimeoutService(
        ILogger<OpcTimeoutService> logger,
        IOptions<OpcTimeoutOptions> options)
    {
        _logger = logger;
        _options = options.Value;
        
        // Timer para verificar timeouts a cada segundo
        _timeoutCheckTimer = new System.Threading.Timer(CheckTimeouts, null,
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    /// <summary>
    /// Executa operação com timeout configurável
    /// </summary>
    public async Task<T> ExecuteWithTimeoutAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        OpcOperationType operationType,
        string? operationId = null,
        TimeSpan? customTimeout = null,
        CancellationToken cancellationToken = default)
    {
        var opId = operationId ?? Guid.NewGuid().ToString();
        var timeout = customTimeout ?? GetTimeoutForOperation(operationType);
        
        _logger.LogDebug("Iniciando operação {OperationType} com timeout de {Timeout}ms. ID: {OperationId}", 
            operationType, timeout.TotalMilliseconds, opId);

        var operationTimeout = new OpcOperationTimeout
        {
            OperationId = opId,
            OperationType = operationType,
            StartTime = DateTime.Now,
            Timeout = timeout,
            IsActive = true
        };

        _activeOperations.TryAdd(opId, operationTimeout);

        using var timeoutCts = new CancellationTokenSource(timeout);
        try
        {
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(
                cancellationToken, timeoutCts.Token);

            var result = await operation(combinedCts.Token);

            operationTimeout.IsActive = false;
            operationTimeout.CompletedAt = DateTime.Now;
            operationTimeout.Success = true;

            OperationCompleted?.Invoke(this, new OpcTimeoutEventArgs
            {
                OperationId = opId,
                OperationType = operationType,
                Duration = operationTimeout.Duration,
                Success = true
            });

            _logger.LogDebug("Operação {OperationType} concluída com sucesso em {Duration}ms. ID: {OperationId}",
                operationType, operationTimeout.Duration.TotalMilliseconds, opId);

            return result;
        }
        catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
        {
            operationTimeout.IsActive = false;
            operationTimeout.CompletedAt = DateTime.Now;
            operationTimeout.Success = false;
            operationTimeout.TimedOut = true;

            OperationTimedOut?.Invoke(this, new OpcTimeoutEventArgs
            {
                OperationId = opId,
                OperationType = operationType,
                Duration = operationTimeout.Duration,
                Success = false,
                TimedOut = true
            });

            _logger.LogWarning("Operação {OperationType} expirou após {Duration}ms. ID: {OperationId}", 
                operationType, operationTimeout.Duration.TotalMilliseconds, opId);

            throw new OpcTimeoutException($"Operação {operationType} expirou após {timeout.TotalSeconds} segundos", operationType, timeout);
        }
        catch (Exception ex)
        {
            operationTimeout.IsActive = false;
            operationTimeout.CompletedAt = DateTime.Now;
            operationTimeout.Success = false;
            operationTimeout.Error = ex.Message;

            _logger.LogError(ex, "Erro na operação {OperationType} após {Duration}ms. ID: {OperationId}", 
                operationType, operationTimeout.Duration.TotalMilliseconds, opId);

            throw;
        }
        finally
        {
            // Remover operação após um tempo para manter histórico
            _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ =>
                _activeOperations.TryRemove(opId, out var _));
        }
    }

    /// <summary>
    /// Executa operação sem retorno com timeout
    /// </summary>
    public async Task ExecuteWithTimeoutAsync(
        Func<CancellationToken, Task> operation,
        OpcOperationType operationType,
        string? operationId = null,
        TimeSpan? customTimeout = null,
        CancellationToken cancellationToken = default)
    {
        await ExecuteWithTimeoutAsync(async ct =>
        {
            await operation(ct);
            return true; // Dummy return value
        }, operationType, operationId, customTimeout, cancellationToken);
    }

    /// <summary>
    /// Obtém timeout configurado para tipo de operação
    /// </summary>
    public TimeSpan GetTimeoutForOperation(OpcOperationType operationType)
    {
        return operationType switch
        {
            OpcOperationType.Connect => TimeSpan.FromSeconds(_options.ConnectionTimeoutSeconds),
            OpcOperationType.Disconnect => TimeSpan.FromSeconds(_options.DisconnectionTimeoutSeconds),
            OpcOperationType.Read => TimeSpan.FromSeconds(_options.ReadTimeoutSeconds),
            OpcOperationType.Write => TimeSpan.FromSeconds(_options.WriteTimeoutSeconds),
            OpcOperationType.Browse => TimeSpan.FromSeconds(_options.BrowseTimeoutSeconds),
            OpcOperationType.Subscribe => TimeSpan.FromSeconds(_options.SubscribeTimeoutSeconds),
            OpcOperationType.Unsubscribe => TimeSpan.FromSeconds(_options.UnsubscribeTimeoutSeconds),
            OpcOperationType.Discovery => TimeSpan.FromSeconds(_options.DiscoveryTimeoutSeconds),
            _ => TimeSpan.FromSeconds(_options.DefaultTimeoutSeconds)
        };
    }

    /// <summary>
    /// Define timeout personalizado para tipo de operação
    /// </summary>
    public void SetTimeoutForOperation(OpcOperationType operationType, TimeSpan timeout)
    {
        _logger.LogInformation("Definindo timeout personalizado para {OperationType}: {Timeout}s", 
            operationType, timeout.TotalSeconds);

        switch (operationType)
        {
            case OpcOperationType.Connect:
                _options.ConnectionTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Disconnect:
                _options.DisconnectionTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Read:
                _options.ReadTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Write:
                _options.WriteTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Browse:
                _options.BrowseTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Subscribe:
                _options.SubscribeTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Unsubscribe:
                _options.UnsubscribeTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            case OpcOperationType.Discovery:
                _options.DiscoveryTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
            default:
                _options.DefaultTimeoutSeconds = (int)timeout.TotalSeconds;
                break;
        }
    }

    /// <summary>
    /// Obtém estatísticas de timeout
    /// </summary>
    public async Task<OpcTimeoutStatistics> GetTimeoutStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var operations = _activeOperations.Values.ToList();
        var completedOperations = operations.Where(o => !o.IsActive).ToList();
        var activeOperations = operations.Where(o => o.IsActive).ToList();

        return new OpcTimeoutStatistics
        {
            TotalOperations = operations.Count,
            ActiveOperations = activeOperations.Count,
            CompletedOperations = completedOperations.Count,
            SuccessfulOperations = completedOperations.Count(o => o.Success),
            TimedOutOperations = completedOperations.Count(o => o.TimedOut),
            FailedOperations = completedOperations.Count(o => !o.Success && !o.TimedOut),
            AverageOperationDuration = completedOperations.Any() ? 
                TimeSpan.FromTicks((long)completedOperations.Average(o => o.Duration.Ticks)) : 
                TimeSpan.Zero,
            TimeoutRate = completedOperations.Any() ? 
                (double)completedOperations.Count(o => o.TimedOut) / completedOperations.Count * 100 : 
                0.0,
            OperationsByType = operations
                .GroupBy(o => o.OperationType)
                .ToDictionary(g => g.Key, g => g.Count())
        };
    }

    /// <summary>
    /// Lista operações ativas
    /// </summary>
    public async Task<List<OpcActiveOperation>> GetActiveOperationsAsync(CancellationToken cancellationToken = default)
    {
        return _activeOperations.Values
            .Where(o => o.IsActive)
            .Select(o => new OpcActiveOperation
            {
                OperationId = o.OperationId,
                OperationType = o.OperationType,
                StartTime = o.StartTime,
                ElapsedTime = DateTime.Now - o.StartTime,
                RemainingTime = o.Timeout - (DateTime.Now - o.StartTime),
                Progress = Math.Min(100.0, (DateTime.Now - o.StartTime).TotalMilliseconds / o.Timeout.TotalMilliseconds * 100)
            })
            .ToList();
    }

    /// <summary>
    /// Cancela operação específica
    /// </summary>
    public async Task<bool> CancelOperationAsync(string operationId, CancellationToken cancellationToken = default)
    {
        if (_activeOperations.TryGetValue(operationId, out var operation) && operation.IsActive)
        {
            operation.CancellationTokenSource?.Cancel();
            operation.IsActive = false;
            operation.CompletedAt = DateTime.Now;
            
            _logger.LogInformation("Operação cancelada: {OperationId}", operationId);
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// Cancela todas as operações ativas
    /// </summary>
    public async Task CancelAllOperationsAsync(CancellationToken cancellationToken = default)
    {
        var activeOperations = _activeOperations.Values.Where(o => o.IsActive).ToList();
        
        foreach (var operation in activeOperations)
        {
            operation.CancellationTokenSource?.Cancel();
            operation.IsActive = false;
            operation.CompletedAt = DateTime.Now;
        }
        
        _logger.LogInformation("Canceladas {Count} operações ativas", activeOperations.Count);
    }

    /// <summary>
    /// Verifica timeouts das operações ativas
    /// </summary>
    private void CheckTimeouts(object? state)
    {
        try
        {
            var now = DateTime.Now;
            var timedOutOperations = _activeOperations.Values
                .Where(o => o.IsActive && (now - o.StartTime) > o.Timeout)
                .ToList();

            foreach (var operation in timedOutOperations)
            {
                operation.IsActive = false;
                operation.CompletedAt = now;
                operation.TimedOut = true;
                operation.CancellationTokenSource?.Cancel();

                OperationTimedOut?.Invoke(this, new OpcTimeoutEventArgs
                {
                    OperationId = operation.OperationId,
                    OperationType = operation.OperationType,
                    Duration = operation.Duration,
                    Success = false,
                    TimedOut = true
                });

                _logger.LogWarning("Operação {OperationType} expirou. ID: {OperationId}, Duração: {Duration}ms", 
                    operation.OperationType, operation.OperationId, operation.Duration.TotalMilliseconds);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar timeouts");
        }
    }

    /// <summary>
    /// Limpa operações antigas do histórico
    /// </summary>
    private void CleanupOldOperations()
    {
        var cutoff = DateTime.Now.AddHours(-1);
        var oldOperations = _activeOperations
            .Where(kvp => !kvp.Value.IsActive && kvp.Value.CompletedAt < cutoff)
            .Select(kvp => kvp.Key)
            .ToList();

        foreach (var operationId in oldOperations)
        {
            _activeOperations.TryRemove(operationId, out _);
        }

        if (oldOperations.Any())
        {
            _logger.LogDebug("Removidas {Count} operações antigas do histórico", oldOperations.Count);
        }
    }

    public void Dispose()
    {
        _timeoutCheckTimer?.Dispose();
        
        // Cancelar todas as operações ativas
        Task.Run(async () => await CancelAllOperationsAsync()).Wait(TimeSpan.FromSeconds(5));
        
        _activeOperations.Clear();
    }
}
