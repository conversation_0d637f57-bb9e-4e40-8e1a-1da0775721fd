using BOS.Plant.Modules.OpcDa.Models;
using OpcCom;

namespace BOS.Plant.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para cache inteligente de conexões OPC
/// </summary>
public interface IOpcConnectionCacheService
{
    /// <summary>
    /// Evento disparado quando uma conexão é adicionada ao cache
    /// </summary>
    event EventHandler<OpcConnectionCacheEventArgs>? ConnectionAdded;

    /// <summary>
    /// Evento disparado quando uma conexão é removida do cache
    /// </summary>
    event EventHandler<OpcConnectionCacheEventArgs>? ConnectionRemoved;

    /// <summary>
    /// Evento disparado quando uma conexão expira
    /// </summary>
    event EventHandler<OpcConnectionCacheEventArgs>? ConnectionExpired;

    /// <summary>
    /// Obtém uma conexão do cache ou cria uma nova
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="host">Host do servidor (opcional, padrão: localhost)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Servidor OPC conectado ou null se falhar</returns>
    Task<OpcCom.Da.Server?> GetConnectionAsync(string serverProgId, string? host = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove uma conexão específica do cache
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="host">Host do servidor (opcional, padrão: localhost)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se a conexão foi removida, false caso contrário</returns>
    Task<bool> RemoveConnectionAsync(string serverProgId, string? host = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Limpa todas as conexões do cache
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task ClearCacheAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas do cache
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do cache</returns>
    Task<OpcConnectionCacheStatistics> GetCacheStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista todas as conexões no cache
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de informações das conexões em cache</returns>
    Task<List<OpcCachedConnectionInfo>> GetCachedConnectionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Força reconexão de uma conexão específica
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="host">Host do servidor (opcional, padrão: localhost)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se a reconexão foi bem-sucedida, false caso contrário</returns>
    Task<bool> RefreshConnectionAsync(string serverProgId, string? host = null, CancellationToken cancellationToken = default);
}
