namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Interface para serviço de políticas de senha
/// </summary>
public interface IPasswordPolicyService
{
    /// <summary>
    /// Valida se a senha atende às políticas de segurança
    /// </summary>
    /// <param name="password">Senha a ser validada</param>
    /// <returns>Resultado da validação</returns>
    PasswordValidationResult ValidatePassword(string password);

    /// <summary>
    /// Calcula a força da senha
    /// </summary>
    /// <param name="password">Senha a ser analisada</param>
    /// <returns>Força da senha (0-100)</returns>
    int CalculatePasswordStrength(string password);

    /// <summary>
    /// Verifica se a senha é comum/fraca
    /// </summary>
    /// <param name="password">Senha a ser verificada</param>
    /// <returns>True se a senha é comum</returns>
    bool IsCommonPassword(string password);

    /// <summary>
    /// Gera uma senha segura
    /// </summary>
    /// <param name="length">Comprimento da senha</param>
    /// <param name="includeSymbols">Incluir símbolos especiais</param>
    /// <returns>Senha gerada</returns>
    string GenerateSecurePassword(int length = 12, bool includeSymbols = true);
}

/// <summary>
/// Resultado da validação de senha
/// </summary>
public class PasswordValidationResult
{
    /// <summary>
    /// Indica se a senha é válida
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Lista de erros de validação
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Força da senha (0-100)
    /// </summary>
    public int Strength { get; set; }

    /// <summary>
    /// Sugestões para melhorar a senha
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}
