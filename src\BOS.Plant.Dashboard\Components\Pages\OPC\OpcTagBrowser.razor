@page "/opc/tags"
@using BOS.Plant.Modules.OpcDa.Interfaces
@using BOS.Plant.Modules.OpcDa.Models
@using BOS.Plant.Modules.OpcDa.Services
@using BOS.Plant.Dashboard.Components.Pages.OPC.Dialogs
@inject IOpcDiscoveryService OpcDiscovery
@inject OpcRealBrowsingService RealBrowsingService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>Navegador de Tags OPC - Smar Hart System</PageTitle>

<style>
    .fixed-height-card {
        height: 70vh; /* Altura fixa relativa ao viewport */
        display: flex;
        flex-direction: column;
        padding-top: 10px;
    }

    .fixed-height-list {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .mud-grid {
        padding-top: 6px;
    }

    .mud-grid-spacing-xs-6>.mud-grid-item {
        padding-left: 24px;
        padding-top: 10px;
    }
</style>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="d-flex flex-column pa-0">
    <MudGrid Class="pt-6">
        <MudItem xs="12" md="8">
            <MudText Typo="Typo.h4">Navegador de Tags OPC</MudText>
            @if (_currentServer != null)
            {
                <MudText Typo="Typo.subtitle1" Color="Color.Secondary">
                    Servidor: @_currentServer.DisplayName
                </MudText>
            }
        </MudItem>
        @*<MudItem xs="4" md="4" Class="d-flex justify-end">
            <!--MudButton Variant="Variant.Outlined"
                      Color="Color.Primary"
                      StartIcon="@Icons.Material.Filled.Refresh"
                      OnClick="RefreshTags" 
                      Disabled="_isLoading">
                Atualizar
            </MudButton>
            <MudButton Variant="Variant.Outlined"
                      Color="Color.Secondary"
                      StartIcon="@Icons.Material.Filled.AccountTree"
                      OnClick="BrowseRealTags"
                      Disabled="_currentServer == null || _isLoading">
                Browsing Real
            </MudButton-->
            <MudButton Variant="Variant.Filled"
                      Color="Color.Success"
                      StartIcon="@Icons.Material.Filled.Add"
                      OnClick="OpenCreateGroupDialog"
                      Disabled="_currentServer == null">
                Criar Grupo
            </MudButton>
        </MudItem>*@
    </MudGrid>

    @if (_currentServer == null)
    {
        <MudCard Elevation="2">
            <MudCardContent>
                <div class="d-flex justify-center align-center pa-8">
                    <div class="text-center">
                        <MudIcon Icon="@Icons.Material.Filled.LinkOff" Size="Size.Large" Color="Color.Secondary" />
                        <MudText Typo="Typo.h6" Class="mt-2">Nenhum servidor conectado</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
                            Conecte-se a um servidor OPC para navegar pelos tags disponíveis
                        </MudText>
                        <MudButton Variant="Variant.Filled" 
                                  Color="Color.Primary" 
                                  StartIcon="@Icons.Material.Filled.Storage"
                                  Href="/opc/servers">
                            Gerenciar Servidores
                        </MudButton>
                    </div>
                </div>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <MudGrid Class="pt-6">
            <!-- Painel de Busca -->
            <MudItem xs="12">
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardContent>
                        <MudGrid Justify="Justify.Center">
                            <MudItem xs="12" md="8">
                                <MudTextField @bind-Value="_searchPattern" Label="Buscar Tags" Placeholder="Digite o nome do tag ou padrão..."
                                            Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                                            OnKeyDown="OnSearchKeyPress" />
                            </MudItem>
                            <MudItem xs="12" md="4">
                                <MudButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true"
                                          StartIcon="@Icons.Material.Filled.Search" OnClick="SearchTags" Disabled="_isLoading">
                                    Buscar
                                </MudButton>
                            </MudItem>
                        </MudGrid>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <MudItem xs="12">
                <MudGrid>
                    <!-- Lista de Tags selecionados para serem monitorados -->
                    <MudItem xs="12" md="8">
                        <MudPaper Elevation="2" Class="fixed-height-card">
                            <div class="d-flex justify-space-between align-center pa-6">
                                <MudText Typo="Typo.h6">
                                    Tags Selecionados (@_filteredTags.Count)
                                </MudText>
                                <div class="d-flex gap-2">
                                    <MudChip T="string" Size="Size.Small" Color="Color.Success" Icon="@Icons.Material.Filled.Add"
                                        OnClick="OpenCreateGroupDialog" Disabled="_currentServer == null">
                                        Criar Grupo
                                    </MudChip>
                                    @*<MudChip T="string" Size="Size.Small" Color="Color.Success">
                                        @_filteredTags.Count(t => !t.IsMonitored) Disponíveis
                                    </MudChip>*@
                                    <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                        @_filteredTags.Count(t => t.IsMonitored) Monitorados
                                    </MudChip>
                                </div>
                            </div>
                            <div class="pa-6" >
                                @if (_isLoadingNodes)
                                {
                                    <div class="d-flex justify-center align-center pa-8">
                                        <MudProgressCircular Indeterminate="true" />
                                        <MudText Typo="Typo.body1" Class="ml-4">Carregando tags...</MudText>
                                    </div>
                                }
                                else if (_filteredTags.Any())
                                {
                                    <MudTable Items="_filteredTags" FixedHeader="true" Hover="true" Breakpoint="Breakpoint.Sm"
                                        Dense="true" Filter="new Func<OpcTagInfo, bool>(FilterTags)">
                                        <HeaderContent>
                                            <MudTh>Status</MudTh>
                                            <MudTh>Nome do Tag</MudTh>
                                            <MudTh>Tipo de Dados</MudTh>
                                            <MudTh>Acesso</MudTh>
                                            <MudTh>Caminho</MudTh>
                                            <MudTh>Grupo</MudTh>
                                            <MudTh>Ações</MudTh>
                                        </HeaderContent>
                                        <RowTemplate>
                                            <MudTd>
                                                @if (context.IsMonitored)
                                                {
                                                    <MudIcon Icon="@Icons.Material.Filled.Visibility" Color="Color.Primary" Size="Size.Small" />
                                                }
                                                else
                                                {
                                                    <MudIcon Icon="@Icons.Material.Filled.VisibilityOff" Color="Color.Secondary" Size="Size.Small" />
                                                }
                                            </MudTd>
                                            <MudTd>
                                                <div>
                                                    <MudText Typo="Typo.body2" Style="font-weight: bold; font-family: monospace;">
                                                        @context.TagName
                                                    </MudText>
                                                    @if (!string.IsNullOrEmpty(context.DisplayName) && context.DisplayName != context.TagName)
                                                    {
                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                            @context.DisplayName
                                                        </MudText>
                                                    }
                                                </div>
                                            </MudTd>
                                            <MudTd>
                                                <MudChip T="string" Size="Size.Small" Color="Color.Default">
                                                    @context.DataType
                                                </MudChip>
                                            </MudTd>
                                            <MudTd>
                                                <MudChip T="string" Size="Size.Small" Color="@GetAccessRightsColor(context.AccessRights)">
                                                    @context.AccessRights.ToString()
                                                </MudChip>
                                            </MudTd>
                                            <MudTd>
                                                <MudText Typo="Typo.caption">@context.Path</MudText>
                                            </MudTd>
                                            <MudTd>
                                                @if (context.IsMonitored)
                                                {
                                                    <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                                                        @context.GroupName
                                                    </MudChip>
                                                }
                                                else
                                                {
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">-</MudText>
                                                }
                                            </MudTd>
                                            <MudTd>
                                                @if (context.IsMonitored)
                                                {
                                                    <MudButton Variant="Variant.Text" Color="Color.Error" Size="Size.Small" StartIcon="@Icons.Material.Filled.RemoveCircle"
                                                            OnClick="@(() => RemoveFromMonitoring(context))">
                                                        Remover
                                                    </MudButton>
                                                }
                                                else
                                                {
                                                    <MudButton Variant="Variant.Filled" Color="Color.Success" Size="Size.Small" StartIcon="@Icons.Material.Filled.Add"
                                                            OnClick="@(() => AddToMonitoring(context))">
                                                        Adicionar
                                                    </MudButton>
                                                }
                                            </MudTd>
                                        </RowTemplate>
                                        <NoRecordsContent>
                                            <div class="d-flex justify-center align-center pa-8">
                                                <div class="text-center">
                                                    <MudIcon Icon="@Icons.Material.Filled.SearchOff" Size="Size.Large" Color="Color.Secondary" />
                                                    <MudText Typo="Typo.body1" Class="mt-2">Nenhum tag encontrado</MudText>
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                        Tente ajustar os critérios de busca
                                                    </MudText>
                                                </div>
                                            </div>
                                        </NoRecordsContent>
                                    </MudTable>
                                }
                                else
                                {
                                    <div class="d-flex justify-center align-center pa-8">
                                        <div class="text-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Label" Size="Size.Large" Color="Color.Secondary" />
                                            <MudText Typo="Typo.body1" Class="mt-2">Nenhum tag disponível</MudText>
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                Verifique a conexão com o servidor OPC
                                            </MudText>
                                        </div>
                                    </div>
                                }
                            </div>
                        </MudPaper>
                    </MudItem>
                    <!-- Lista de Tags disponiveis no servidor OPC -->
                    <MudItem xs="12" md="4">
                        <MudPaper Elevation="2" Class="fixed-height-card">
                            <div class="d-flex justify-space-between align-center pa-6">
                                <MudText Typo="Typo.h6">Navegador de Tags</MudText>
                                <MudButton Variant="Variant.Outlined" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Refresh"
                                    OnClick="@(() => NavigateToPath(_currentPath))" Disabled="_isLoadingNodes">
                                    Atualizar
                                </MudButton>
                            </div>
                            <MudPaper Class="ma-6" Style="overflow-y:auto;">
                                @if (_isLoadingNodes)
                                {
                                    <div class="d-flex justify-center align-center pa-8">
                                        <MudProgressCircular Indeterminate="true" />
                                        <MudText Typo="Typo.body1" Class="ml-4">Carregando tags...</MudText>
                                    </div>
                                }
                                else
                                {
                                    <CascadingValue Value="@AddToMonitoring">
                                        <MudList T="string" Dense="true" Gutters="false" Class="ml-4 mr-4">
                                            @foreach (var node in _currentNodes)
                                            {
                                                @if (node.IsBranch)
                                                {
                                                    <MudListItem T="string" Text="@node.Name" Icon="@Icons.Material.Filled.Folder" IconColor="@Color.Warning"
                                                        OnClickPreventDefault="true" OnClick="@(() => HandleNodeClick(node))" @bind-Expanded="node.IsExpanded">
                                                        <NestedList>
                                                            @foreach (var child in node.Children)
                                                            {
                                                                <NodeListItem Node="@child" OnNodeClick="HandleNodeClick" />
                                                            }
                                                        </NestedList>
                                                    </MudListItem>
                                                }
                                                else
                                                {
                                                    <MudListItem T="string" Text="@node.Name" Icon="@Icons.Material.Filled.Tag" IconColor="@Color.Info"
                                                        OnClickPreventDefault="true" OnClick="@(() => HandleNodeClick(node))" />
                                                }
                                            }
                                        </MudList>
                                    </CascadingValue>
                                }
                            </MudPaper>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    private OpcServerInfo? _currentServer;
    private List<OpcTagInfo> _filteredTags = new();
    private string _searchPattern = string.Empty;
    private bool _isLoading = false;

    // Variáveis para navegação hierárquica
    private List<OpcTagNode> _currentNodes = new();
    private string _currentPath = "";
    private bool _isLoadingNodes = false;
    private List<MudBlazor.BreadcrumbItem> _breadcrumbItems = new();

    protected override async Task OnInitializedAsync()
    {
        _currentServer = OpcDiscovery.GetCurrentServerInfo();

        if (_currentServer != null)
        {
            // Carregar tags disponíveis do serviço
            await LoadAvailableTags();
            // Iniciar browsing na raiz
            await NavigateToPath("");
        }
    }

    private async Task LoadAvailableTags()
    {
        try
        {
            var availableTags = await OpcDiscovery.GetAvailableTagsAsync();
            _filteredTags = availableTags.ToList();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar tags disponíveis: {ex.Message}", Severity.Error);
        }
    }

    private async Task RefreshTags()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var tags = await OpcDiscovery.BrowseTagsAsync();
            await LoadAvailableTags(); // Recarregar do serviço

            Snackbar.Add($"Carregados {_filteredTags.Count} tags", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar tags: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SearchTags()
    {
        if (string.IsNullOrWhiteSpace(_searchPattern))
        {
            await LoadAvailableTags(); // Recarregar todos os tags do serviço
            //Snackbar.Add($"A lista de tags selecionados esta vazia", Severity.Info);
        }
        else
        {
            try
            {
                var searchResults = await OpcDiscovery.SearchTagsAsync(_searchPattern);
                _filteredTags = searchResults.ToList();

                Snackbar.Add($"Encontrados {_filteredTags.Count} tags", Severity.Info);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro na busca: {ex.Message}", Severity.Error);
            }
        }

        StateHasChanged();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchTags();
        }
    }

    // Métodos para browsing hierárquico
    private async Task BrowseRealTags()
    {
        if (_currentServer == null) return;

        try
        {
            _isLoadingNodes = true;
            StateHasChanged();

            // Iniciar browsing na raiz
            await NavigateToPath("");

            Snackbar.Add("Browsing real iniciado", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro no browsing real: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoadingNodes = false;
            StateHasChanged();
        }
    }

    private async Task NavigateToPath(string? path = null, OpcTagNode? node = null)
    {
        if (_currentServer == null) return;

        try
        {
            _isLoadingNodes = true;
            _currentPath = path ?? "";
            StateHasChanged();

            // Fazer browsing real do caminho
            var nodes = await RealBrowsingService.BrowseRealTagsAsync(_currentServer.ProgId, _currentServer.Host, _currentPath);
            
            if (node != null)
            {
                node.Children = nodes.ToList();
            }
            else
            {
                _currentNodes = nodes.ToList();
            }

            // Atualizar breadcrumb
            UpdateBreadcrumb();

            Snackbar.Add($"Carregados {_currentNodes.Count} itens", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao navegar: {ex.Message}", Severity.Error);
            _currentNodes.Clear();
        }
        finally
        {
            _isLoadingNodes = false;
            StateHasChanged();
        }
    }

    private async Task HandleNodeClick(OpcTagNode node)
    {
        if (string.IsNullOrWhiteSpace(node.ItemId))
            return;

        if (node.IsBranch)
        {
            node.IsExpanded = !node.IsExpanded;

            if (!node.Children.Any())
            {
                // Navegar para o subdiretório
                await NavigateToPath(node.ItemId, node);
            }
        }
        else
        {
            // É um tag - adicionar à lista de tags disponíveis ou mostrar detalhes
            var tagInfo = new OpcTagInfo
            {
                TagName = node.ItemId,
                DisplayName = node.Name,
                DataType = node.DataType,
                Path = node.ParentPath ?? "",
                AccessRights = node.AccessRights,
                Description = node.Description
            };

            // Adicionar ao serviço se não existir
            var success = await OpcDiscovery.AddAvailableTagAsync(tagInfo);
            if (success)
            {
                // Recarregar a lista filtrada
                await LoadAvailableTags();
                Snackbar.Add($"Tag selecionado: {node.Name}", Severity.Success);
            }
            else
            {
                Snackbar.Add($"Erro ao selecionar tag: {node.Name}", Severity.Error);
            }
        }
    }

    private void UpdateBreadcrumb()
    {
        _breadcrumbItems.Clear();

        // Adicionar raiz
        _breadcrumbItems.Add(new MudBlazor.BreadcrumbItem("Raiz", "", false, Icons.Material.Filled.Home));

        if (!string.IsNullOrEmpty(_currentPath))
        {
            var pathParts = _currentPath.Split('.', StringSplitOptions.RemoveEmptyEntries);
            var currentPath = "";

            foreach (var part in pathParts)
            {
                currentPath = string.IsNullOrEmpty(currentPath) ? part : $"{currentPath}.{part}";
                _breadcrumbItems.Add(new MudBlazor.BreadcrumbItem(part, currentPath, false, Icons.Material.Filled.Folder));
            }
        }
    }

    private bool FilterTags(OpcTagInfo tag)
    {
        // Como já estamos filtrando no SearchTags, sempre retorna true aqui
        // O filtro real acontece quando carregamos os dados do serviço
        return true;
    }

    private async Task AddToMonitoring(OpcTagInfo tag)
    {
        var parameters = new DialogParameters
        {
            ["Tag"] = tag,
            ["AvailableGroups"] = await OpcDiscovery.GetAvailableGroupsAsync()
        };

        var dialog = await DialogService.ShowAsync<AddTagDialog>("Adicionar Tag ao Monitoramento", parameters);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is AddTagRequest request)
        {
            try
            {
                var success = await OpcDiscovery.AddTagToMonitoringAsync(request);
                if (success)
                {
                    tag.IsMonitored = true;
                    tag.GroupName = request.GroupName;
                    Snackbar.Add($"Tag {tag.TagName} adicionado ao monitoramento", Severity.Success);
                }
                else
                {
                    Snackbar.Add($"Falha ao adicionar tag {tag.TagName}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao adicionar tag: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task RemoveFromMonitoring(OpcTagInfo tag)
    {
        try
        {
            var success = await OpcDiscovery.RemoveTagFromMonitoringAsync(tag.TagName);
            if (success)
            {
                tag.IsMonitored = false;
                tag.GroupName = null;
                Snackbar.Add($"Tag {tag.TagName} removido do monitoramento", Severity.Success);
            }
            else
            {
                Snackbar.Add($"Falha ao remover tag {tag.TagName}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao remover tag: {ex.Message}", Severity.Error);
        }
    }

    private async Task OpenCreateGroupDialog()
    {
        var dialog = await DialogService.ShowAsync<CreateGroupDialog>("Criar Novo Grupo");
        var result = await dialog.Result;

        if (!result.Canceled && result.Data is (string groupName, int updateRate))
        {
            try
            {
                var success = await OpcDiscovery.CreateGroupAsync(groupName, updateRate);
                if (success)
                {
                    Snackbar.Add($"Grupo {groupName} criado com sucesso", Severity.Success);
                }
                else
                {
                    Snackbar.Add($"Falha ao criar grupo {groupName}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao criar grupo: {ex.Message}", Severity.Error);
            }
        }
    }

    private Color GetAccessRightsColor(OpcAccessRights rights)
    {
        return rights switch
        {
            OpcAccessRights.Read => Color.Info,
            OpcAccessRights.Write => Color.Warning,
            OpcAccessRights.ReadWrite => Color.Success,
            _ => Color.Default
        };
    }
}
