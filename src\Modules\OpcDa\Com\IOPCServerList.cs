using System.Runtime.InteropServices;

namespace BOS.Plant.Modules.OpcDa.Com;

/// <summary>
/// Interface IOPCServerList para descoberta de servidores OPC
/// </summary>
[ComImport]
[Guid("13486D50-4821-11D2-A494-3CB306C10000")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IOPCServerList
{
    void EnumClassesOfCategories(
        [In] int cImplemented,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 0)] Guid[] rgcatidImpl,
        [In] int cRequired,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 2)] Guid[] rgcatidReq,
        [Out, MarshalAs(UnmanagedType.Interface)] out IEnumGUID ppenumClsid);

    void GetClassDetails(
        [In, MarshalAs(UnmanagedType.LPStruct)] Guid clsid,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszProgID,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszUserType);

    void CLSIDFromProgID(
        [In, MarshalAs(UnmanagedType.LPWStr)] string szProgId,
        [Out] out Guid clsid);
}

/// <summary>
/// Interface IOPCServerList2 para descoberta avançada de servidores OPC
/// </summary>
[ComImport]
[Guid("9DD0B56C-AD9E-43EE-8305-487F3188BF7A")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IOPCServerList2 : IOPCServerList
{
    // Métodos herdados de IOPCServerList
    new void EnumClassesOfCategories(
        [In] int cImplemented,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 0)] Guid[] rgcatidImpl,
        [In] int cRequired,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 2)] Guid[] rgcatidReq,
        [Out, MarshalAs(UnmanagedType.Interface)] out IEnumGUID ppenumClsid);

    new void GetClassDetails(
        [In, MarshalAs(UnmanagedType.LPStruct)] Guid clsid,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszProgID,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszUserType);

    new void CLSIDFromProgID(
        [In, MarshalAs(UnmanagedType.LPWStr)] string szProgId,
        [Out] out Guid clsid);

    // Métodos específicos do IOPCServerList2
    void EnumClassesOfCategories2(
        [In] int cImplemented,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 0)] Guid[] rgcatidImpl,
        [In] int cRequired,
        [In, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 2)] Guid[] rgcatidReq,
        [In] int dwClsCtx,
        [Out, MarshalAs(UnmanagedType.Interface)] out IEnumGUID ppenumClsid);

    void GetClassDetails2(
        [In, MarshalAs(UnmanagedType.LPStruct)] Guid clsid,
        [In] int dwClsCtx,
        [In, MarshalAs(UnmanagedType.LPWStr)] string szMachineName,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszProgID,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszUserType,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppszVerIndProgID);
}

/// <summary>
/// Interface IEnumGUID para enumeração de GUIDs
/// </summary>
[ComImport]
[Guid("0002E000-0000-0000-C000-000000000046")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IEnumGUID
{
    [PreserveSig]
    int Next(
        [In] int celt,
        [Out, MarshalAs(UnmanagedType.LPArray, ArraySubType = UnmanagedType.LPStruct, SizeParamIndex = 0)] Guid[] rgelt,
        [Out] out int pceltFetched);

    [PreserveSig]
    int Skip([In] int celt);

    void Reset();

    void Clone([Out, MarshalAs(UnmanagedType.Interface)] out IEnumGUID ppenum);
}

/// <summary>
/// Classe para criação de instâncias do OPC Server List
/// </summary>
[ComImport]
[Guid("13486D51-4821-11D2-A494-3CB306C10000")]
public class OPCServerList
{
}

/// <summary>
/// Constantes para categorias OPC
/// </summary>
public static class OPCCategories
{
    /// <summary>
    /// CATID para servidores OPC DA 1.0
    /// </summary>
    public static readonly Guid OPC_CATEGORY_DA10 = new Guid("63D5F430-CFE4-11D1-B2C8-0060083BA1FB");

    /// <summary>
    /// CATID para servidores OPC DA 2.0
    /// </summary>
    public static readonly Guid OPC_CATEGORY_DA20 = new Guid("63D5F432-CFE4-11D1-B2C8-0060083BA1FB");

    /// <summary>
    /// CATID para servidores OPC DA 3.0
    /// </summary>
    public static readonly Guid OPC_CATEGORY_DA30 = new Guid("CC603642-66D7-48F1-B69A-B625E73652D7");
}

/// <summary>
/// Constantes para contexto de classe COM
/// </summary>
public static class ClsCtx
{
    public const int CLSCTX_INPROC_SERVER = 0x1;
    public const int CLSCTX_INPROC_HANDLER = 0x2;
    public const int CLSCTX_LOCAL_SERVER = 0x4;
    public const int CLSCTX_REMOTE_SERVER = 0x10;
    public const int CLSCTX_ALL = CLSCTX_INPROC_SERVER | CLSCTX_INPROC_HANDLER | CLSCTX_LOCAL_SERVER | CLSCTX_REMOTE_SERVER;
}
