



namespace BOS.Plant.Core.Common;

/// <summary>
/// Entidade base para todos os modelos do sistema
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Identificador único
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString("N")[..21]; // Simula NanoId com 21 caracteres

    /// <summary>
    /// Data de criação da entidade
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Data da última atualização
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// ID do usuário que criou a entidade
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// ID do usuário que fez a última atualização
    /// </summary>
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// Indica se a entidade foi excluída (soft delete)
    /// </summary>
    public bool IsDeleted { get; set; } = false;

    /// <summary>
    /// Data de exclusão (soft delete)
    /// </summary>
    public DateTime? DeletedAt { get; set; }

    /// <summary>
    /// ID do usuário que excluiu a entidade
    /// </summary>
    public string? DeletedBy { get; set; }


}
