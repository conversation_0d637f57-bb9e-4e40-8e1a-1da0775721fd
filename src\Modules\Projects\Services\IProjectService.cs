using BOS.Plant.Core.Entities;
using BOS.Plant.Shared.Models;

namespace BOS.Plant.Modules.Projects.Services;

/// <summary>
/// Interface para serviços de projetos
/// </summary>
public interface IProjectService
{
    /// <summary>
    /// Obtém todos os projetos do usuário atual
    /// </summary>
    Task<IEnumerable<Project>> GetUserProjectsAsync(string userId);

    /// <summary>
    /// Obtém projetos paginados
    /// </summary>
    Task<PagedResult<Project>> GetProjectsPagedAsync(int page, int pageSize, string? searchTerm = null, ProjectStatus? status = null);

    /// <summary>
    /// Obtém projeto por ID
    /// </summary>
    Task<Project?> GetProjectByIdAsync(string projectId);

    /// <summary>
    /// Cria um novo projeto
    /// </summary>
    Task<ProjectResult> CreateProjectAsync(CreateProjectRequest request);

    /// <summary>
    /// Atualiza um projeto
    /// </summary>
    Task<ProjectResult> UpdateProjectAsync(string projectId, UpdateProjectRequest request);

    /// <summary>
    /// Atualiza o status do projeto
    /// </summary>
    Task<ProjectResult> UpdateProjectStatusAsync(string projectId, ProjectStatus status);

    /// <summary>
    /// Atualiza o progresso do projeto
    /// </summary>
    Task<ProjectResult> UpdateProjectProgressAsync(string projectId, int progress);

    /// <summary>
    /// Exclui um projeto (soft delete)
    /// </summary>
    Task<ProjectResult> DeleteProjectAsync(string projectId);

    /// <summary>
    /// Obtém estatísticas dos projetos
    /// </summary>
    Task<ProjectStatistics> GetProjectStatisticsAsync(string? userId = null);
}

/// <summary>
/// Resultado de operações de projeto
/// </summary>
public class ProjectResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public Project? Project { get; set; }
}



/// <summary>
/// Estatísticas dos projetos
/// </summary>
public class ProjectStatistics
{
    public int TotalProjects { get; set; }
    public int ActiveProjects { get; set; }
    public int CompletedProjects { get; set; }
    public int OnHoldProjects { get; set; }
    public int CancelledProjects { get; set; }
    public decimal TotalBudget { get; set; }
    public double AverageProgress { get; set; }
}

/// <summary>
/// Request para criação de projeto
/// </summary>
public class CreateProjectRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string OwnerId { get; set; } = string.Empty;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ProjectStatus Status { get; set; } = ProjectStatus.Planning;
    public ProjectPriority Priority { get; set; } = ProjectPriority.Medium;
    public decimal? Budget { get; set; }
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Request para atualização de projeto
/// </summary>
public class UpdateProjectRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ProjectPriority Priority { get; set; }
    public decimal? Budget { get; set; }
    public List<string> Tags { get; set; } = new();
}
