{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information", "BOS.Plant.Modules.OpcDa": "Debug", "Microsoft.AspNetCore.SignalR": "Debug", "System.Net.Http": "Warning"}, "File": {"Path": "logs/opcda-service-.log", "Append": true, "MinLevel": "Debug", "FileSizeLimitBytes": 10485760, "MaxRollingFiles": 10, "OutputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}, "OpcServer": {"ProgId": "Matrikon.OPC.Simulation", "ServerName": "Matrikon OPC Server for Simulation", "Host": "localhost", "ConnectionTimeout": 5000, "ReconnectInterval": 10000, "MaxReconnectAttempts": 5, "EnableSimulation": true, "Groups": []}, "Urls": "http://localhost:5000"}