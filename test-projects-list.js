// Script simples para testar a página de projetos
// Execute este script no console do navegador após navegar para http://localhost:5231

async function testProjectsList() {
    console.log('🚀 Iniciando teste da lista de projetos...');

    // Função para aguardar um tempo
    const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    
    try {
        // Verificar se estamos na página correta
        console.log('📱 URL atual:', window.location.href);

        // Verificar se há erros no console
        console.log('🔍 Verificando erros no console...');

        // Verificar se a página de login aparece
        const loginElements = document.querySelectorAll('input[type="email"], input[type="text"][placeholder*="email"], input[placeholder*="Email"]');
        const passwordElements = document.querySelectorAll('input[type="password"]');

        if (loginElements.length > 0 && passwordElements.length > 0) {
            console.log('✅ Página de login encontrada');
            console.log('🔐 Por favor, faça login manualmente e execute o script novamente');
            return;
        } else {
            console.log('ℹ️ Página de login não encontrada, assumindo que já está logado');
        }

        // Aguardar um pouco para a página carregar completamente
        await wait(2000);

        // Tentar encontrar e clicar no link/menu de projetos
        console.log('🔍 Procurando pelo menu de projetos...');

        // Possíveis seletores para o menu de projetos
        const projectSelectors = [
            'a[href="/projects"]',
            'a[href*="project"]',
            'a:contains("Projetos")',
            'a:contains("Listar Projetos")',
            '.mud-nav-link[href="/projects"]'
        ];

        let projectLinkFound = false;
        let projectLink = null;

        for (const selector of projectSelectors) {
            try {
                if (selector.includes(':contains')) {
                    // Para seletores com :contains, usar uma abordagem diferente
                    const links = Array.from(document.querySelectorAll('a')).filter(a =>
                        a.textContent.includes('Projetos') || a.textContent.includes('Listar Projetos')
                    );
                    if (links.length > 0) {
                        projectLink = links[0];
                        console.log(`✅ Menu de projetos encontrado: ${projectLink.textContent.trim()}`);
                        projectLinkFound = true;
                        break;
                    }
                } else {
                    const element = document.querySelector(selector);
                    if (element) {
                        projectLink = element;
                        console.log(`✅ Menu de projetos encontrado com seletor: ${selector}`);
                        projectLinkFound = true;
                        break;
                    }
                }
            } catch (e) {
                console.log(`❌ Erro com seletor ${selector}:`, e.message);
            }
        }

        if (projectLinkFound && projectLink) {
            console.log('🔗 Clicando no link de projetos...');
            projectLink.click();
            await wait(3000); // Aguardar navegação
        } else {
            console.log('❌ Menu de projetos não encontrado. Tentando navegar diretamente...');

            // Tentar navegar diretamente para a página de projetos
            const projectUrls = [
                '/projects',
                '/projetos',
                '/admin/projects',
                '/admin/projetos'
            ];

            for (const url of projectUrls) {
                try {
                    console.log(`🔗 Tentando navegar para: ${url}`);
                    window.location.href = url;
                    await wait(3000);

                    // Verificar se a página carregou sem erro 404
                    const content = document.body.textContent || '';

                    if (!content.includes('404') && !content.includes('Not Found')) {
                        console.log(`✅ Página de projetos encontrada em: ${url}`);
                        projectLinkFound = true;
                        break;
                    }
                } catch (error) {
                    console.log(`❌ Erro ao navegar para ${url}: ${error.message}`);
                }
            }
        }

        // Aguardar a página carregar
        await wait(3000);

        console.log('🔍 Verificando a página de projetos...');

        // Verificar se há erros na página
        const pageContent = document.body.textContent || '';
        const pageTitle = document.title;

        console.log(`📄 Título da página: ${pageTitle}`);
        console.log(`📄 URL atual: ${window.location.href}`);

        // Verificar por mensagens de erro comuns
        const errorIndicators = [
            'error',
            'erro',
            'exception',
            'null reference',
            'object reference not set',
            'internal server error',
            '500',
            'stack trace',
            'unhandled exception',
            'system.nullreferenceexception'
        ];

        let errorsFound = [];
        for (const indicator of errorIndicators) {
            if (pageContent.toLowerCase().includes(indicator.toLowerCase())) {
                errorsFound.push(indicator);
            }
        }

        if (errorsFound.length > 0) {
            console.log('❌ Erros encontrados na página:');
            errorsFound.forEach(error => console.log(`   - ${error}`));
        } else {
            console.log('✅ Nenhum erro óbvio encontrado no conteúdo da página');
        }

        // Verificar se há uma tabela ou lista de projetos
        const tables = document.querySelectorAll('table');
        const lists = document.querySelectorAll('ul, ol');
        const cards = document.querySelectorAll('.card, .project-card, .mud-card');
        const mudTables = document.querySelectorAll('.mud-table');

        console.log(`📊 Elementos encontrados:`);
        console.log(`   - Tabelas: ${tables.length}`);
        console.log(`   - Tabelas MudBlazor: ${mudTables.length}`);
        console.log(`   - Listas: ${lists.length}`);
        console.log(`   - Cards: ${cards.length}`);

        // Verificar por botões de ação (Criar, Adicionar, etc.)
        const actionButtons = Array.from(document.querySelectorAll('button, a')).filter(el => {
            const text = el.textContent || '';
            return text.includes('Criar') || text.includes('Adicionar') || text.includes('Novo');
        });
        console.log(`   - Botões de ação: ${actionButtons.length}`);

        // Verificar se há indicadores de carregamento
        const loadingIndicators = document.querySelectorAll('.mud-progress-circular, .loading, .spinner');
        console.log(`   - Indicadores de carregamento: ${loadingIndicators.length}`);

        // Verificar se há mensagens de "Nenhum projeto encontrado"
        const noDataMessages = Array.from(document.querySelectorAll('*')).filter(el => {
            const text = el.textContent || '';
            return text.includes('Nenhum projeto') || text.includes('No projects') || text.includes('Nenhum registro');
        });
        console.log(`   - Mensagens de "sem dados": ${noDataMessages.length}`);

        // Verificar erros específicos do Blazor
        const blazorErrors = document.querySelectorAll('.blazor-error-ui, #blazor-error-ui');
        if (blazorErrors.length > 0) {
            console.log('❌ Erros do Blazor encontrados!');
            blazorErrors.forEach((error, index) => {
                console.log(`   Erro ${index + 1}:`, error.textContent);
            });
        }

    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
    } finally {
        console.log('🔚 Teste concluído');
    }
}

// Executar o teste
console.log('Para executar o teste, chame: testProjectsList()');

// Função adicional para navegar diretamente para projetos
function goToProjects() {
    console.log('🔗 Navegando para /projects...');
    window.location.href = '/projects';
}

// Função para verificar erros no console
function checkConsoleErrors() {
    console.log('🔍 Verificando erros no console...');

    // Capturar erros do console
    const originalError = console.error;
    const originalWarn = console.warn;
    const errors = [];
    const warnings = [];

    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };

    console.warn = function(...args) {
        warnings.push(args.join(' '));
        originalWarn.apply(console, args);
    };

    setTimeout(() => {
        console.log(`📊 Resumo de erros:`);
        console.log(`   - Erros: ${errors.length}`);
        console.log(`   - Avisos: ${warnings.length}`);

        if (errors.length > 0) {
            console.log('❌ Erros encontrados:');
            errors.forEach((error, index) => console.log(`   ${index + 1}. ${error}`));
        }

        if (warnings.length > 0) {
            console.log('⚠️ Avisos encontrados:');
            warnings.forEach((warning, index) => console.log(`   ${index + 1}. ${warning}`));
        }

        // Restaurar console original
        console.error = originalError;
        console.warn = originalWarn;
    }, 5000);
}
