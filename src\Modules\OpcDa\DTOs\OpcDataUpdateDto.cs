namespace BOS.Plant.Modules.OpcDa.DTOs;

/// <summary>
/// DTO para atualização de dados OPC via SignalR
/// </summary>
public class OpcDataUpdateDto
{
    /// <summary>
    /// Nome do tag
    /// </summary>
    public string TagName { get; set; } = string.Empty;

    /// <summary>
    /// Valor atual
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// Qualidade do valor
    /// </summary>
    public string Quality { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da atualização
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Tipo de dados
    /// </summary>
    public string? DataType { get; set; }

    /// <summary>
    /// Grupo do item
    /// </summary>
    public string? GroupName { get; set; }
}

/// <summary>
/// DTO para status da conexão OPC
/// </summary>
public class OpcConnectionStatusDto
{
    /// <summary>
    /// Status da conexão
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Servidor conectado
    /// </summary>
    public string? ServerName { get; set; }

    /// <summary>
    /// Timestamp da última atualização de status
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Mensagem adicional
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// Número total de itens monitorados
    /// </summary>
    public int TotalItems { get; set; }

    /// <summary>
    /// Número de itens ativos
    /// </summary>
    public int ActiveItems { get; set; }
}

/// <summary>
/// DTO para estatísticas do servidor OPC
/// </summary>
public class OpcServerStatisticsDto
{
    /// <summary>
    /// Tempo de atividade em milissegundos
    /// </summary>
    public long UptimeMs { get; set; }

    /// <summary>
    /// Número total de atualizações recebidas
    /// </summary>
    public long TotalUpdates { get; set; }

    /// <summary>
    /// Taxa de atualizações por segundo
    /// </summary>
    public double UpdatesPerSecond { get; set; }

    /// <summary>
    /// Número de erros de conexão
    /// </summary>
    public int ConnectionErrors { get; set; }

    /// <summary>
    /// Última vez que houve erro
    /// </summary>
    public DateTime? LastError { get; set; }

    /// <summary>
    /// Grupos ativos
    /// </summary>
    public List<string> ActiveGroups { get; set; } = new();
}
