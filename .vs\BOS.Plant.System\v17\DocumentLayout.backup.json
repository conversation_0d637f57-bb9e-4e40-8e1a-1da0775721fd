{"Version": 1, "WorkspaceRootPath": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0E29C93E-E8A6-4FF1-8E12-4E27C9D78678}|src\\Infrastructure\\BOS.Plant.Infrastructure.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\infrastructure\\repositories\\repository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0E29C93E-E8A6-4FF1-8E12-4E27C9D78678}|src\\Infrastructure\\BOS.Plant.Infrastructure.csproj|solutionrelative:src\\infrastructure\\repositories\\repository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7CA502F9-140A-4868-A096-FE3928B08EED}|src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\modules\\projects\\services\\projectservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7CA502F9-140A-4868-A096-FE3928B08EED}|src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj|solutionrelative:src\\modules\\projects\\services\\projectservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0E29C93E-E8A6-4FF1-8E12-4E27C9D78678}|src\\Infrastructure\\BOS.Plant.Infrastructure.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\infrastructure\\repositories\\tenantrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0E29C93E-E8A6-4FF1-8E12-4E27C9D78678}|src\\Infrastructure\\BOS.Plant.Infrastructure.csproj|solutionrelative:src\\infrastructure\\repositories\\tenantrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4C60744-7C79-4B7A-B16F-2BAF147E1C96}|src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\bos.plant.dashboard\\components\\pages\\projects\\projectlist.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{C4C60744-7C79-4B7A-B16F-2BAF147E1C96}|src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj|solutionrelative:src\\bos.plant.dashboard\\components\\pages\\projects\\projectlist.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{F37EDE73-63C2-44C1-9748-EC1DEBE523F3}|src\\Core\\BOS.Plant.Core.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\core\\entities\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F37EDE73-63C2-44C1-9748-EC1DEBE523F3}|src\\Core\\BOS.Plant.Core.csproj|solutionrelative:src\\core\\entities\\project.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C4C60744-7C79-4B7A-B16F-2BAF147E1C96}|src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj|d:\\projects\\mudblazor\\bos.plant.system-ai\\src\\bos.plant.dashboard\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{C4C60744-7C79-4B7A-B16F-2BAF147E1C96}|src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj|solutionrelative:src\\bos.plant.dashboard\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Repository.cs", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Infrastructure\\Repositories\\Repository.cs", "RelativeDocumentMoniker": "src\\Infrastructure\\Repositories\\Repository.cs", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Infrastructure\\Repositories\\Repository.cs", "RelativeToolTip": "src\\Infrastructure\\Repositories\\Repository.cs", "ViewState": "AgIAAFAAAAAAAAAAAAAqwFcAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T14:49:10.999Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "TenantRepository.cs", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Infrastructure\\Repositories\\TenantRepository.cs", "RelativeDocumentMoniker": "src\\Infrastructure\\Repositories\\TenantRepository.cs", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Infrastructure\\Repositories\\TenantRepository.cs", "RelativeToolTip": "src\\Infrastructure\\Repositories\\TenantRepository.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAwwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T14:48:42.473Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProjectService.cs", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Modules\\Projects\\Services\\ProjectService.cs", "RelativeDocumentMoniker": "src\\Modules\\Projects\\Services\\ProjectService.cs", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Modules\\Projects\\Services\\ProjectService.cs", "RelativeToolTip": "src\\Modules\\Projects\\Services\\ProjectService.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAqwCoAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T14:44:55.537Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProjectList.razor", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\Components\\Pages\\Projects\\ProjectList.razor", "RelativeDocumentMoniker": "src\\BOS.Plant.Dashboard\\Components\\Pages\\Projects\\ProjectList.razor", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\Components\\Pages\\Projects\\ProjectList.razor", "RelativeToolTip": "src\\BOS.Plant.Dashboard\\Components\\Pages\\Projects\\ProjectList.razor", "ViewState": "AgIAAIoAAAAAAAAAAAAAAJsAAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-26T14:37:15.102Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Project.cs", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Core\\Entities\\Project.cs", "RelativeDocumentMoniker": "src\\Core\\Entities\\Project.cs", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\Core\\Entities\\Project.cs", "RelativeToolTip": "src\\Core\\Entities\\Project.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAAwE4AAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T14:37:08.07Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "NavMenu.razor", "DocumentMoniker": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\Components\\Layout\\NavMenu.razor", "RelativeDocumentMoniker": "src\\BOS.Plant.Dashboard\\Components\\Layout\\NavMenu.razor", "ToolTip": "D:\\projects\\MudBlazor\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\Components\\Layout\\NavMenu.razor", "RelativeToolTip": "src\\BOS.Plant.Dashboard\\Components\\Layout\\NavMenu.razor", "ViewState": "AgIAAAIAAAAAAAAAAAAAABoAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-26T14:31:34.023Z", "EditorCaption": ""}]}]}]}