using BOS.Plant.Core.Common;
using BOS.Plant.Core.Interfaces;
using BOS.Plant.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace BOS.Plant.Infrastructure.Repositories;

/// <summary>
/// Implementação do repositório para entidades multi-tenant
/// </summary>
/// <typeparam name="T">Tipo da entidade que implementa ITenantEntity</typeparam>
public class TenantRepository<T> : Repository<T>, ITenantRepository<T> where T : class, ITenantEntity
{
    private readonly ITenantService _tenantService;

    public TenantRepository(ApplicationDbContext context, ITenantService tenantService) 
        : base(context)
    {
        _tenantService = tenantService;
    }

    public virtual async Task<IEnumerable<T>> GetByTenantAsync(string tenantId, CancellationToken cancellationToken = default)
    {
        return await _dbSet.Where(e => e.TenantId == tenantId).ToListAsync(cancellationToken);
    }

    public virtual async Task<IEnumerable<T>> GetByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>> predicate, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId)
            .Where(predicate)
            .ToListAsync(cancellationToken);
    }

    public virtual async Task<T?> GetByTenantAndIdAsync(string tenantId, string id, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(e => e.TenantId == tenantId && EF.Property<string>(e, "Id") == id, cancellationToken);
    }

    public virtual async Task<(IEnumerable<T> Items, int TotalCount)> GetPagedByTenantAsync(
        string tenantId,
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(e => e.TenantId == tenantId);

        if (predicate != null)
            query = query.Where(predicate);

        var totalCount = await query.CountAsync(cancellationToken);

        if (orderBy != null)
        {
            query = ascending ? query.OrderBy(orderBy) : query.OrderByDescending(orderBy);
        }

        var items = await query
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public virtual async Task<int> CountByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>>? predicate = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(e => e.TenantId == tenantId);
        
        if (predicate != null)
            query = query.Where(predicate);
            
        return await query.CountAsync(cancellationToken);
    }

    public virtual async Task<bool> ExistsByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>> predicate, 
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(e => e.TenantId == tenantId)
            .AnyAsync(predicate, cancellationToken);
    }

    // Sobrescrever métodos base para usar o tenant atual automaticamente
    public override async Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        var currentTenantId = _tenantService.GetCurrentTenantId();
        if (string.IsNullOrEmpty(currentTenantId))
            return await base.GetAllAsync(cancellationToken);

        return await GetByTenantAsync(currentTenantId, cancellationToken);
    }

    public override async Task<T?> GetByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        var currentTenantId = _tenantService.GetCurrentTenantId();
        if (string.IsNullOrEmpty(currentTenantId))
            return await base.GetByIdAsync(id, cancellationToken);

        return await GetByTenantAndIdAsync(currentTenantId, id, cancellationToken);
    }

    public override async Task<T> AddAsync(T entity, CancellationToken cancellationToken = default)
    {
        // Garantir que o TenantId está definido
        var currentTenantId = _tenantService.GetCurrentTenantId();
        if (!string.IsNullOrEmpty(currentTenantId) && string.IsNullOrEmpty(entity.TenantId))
        {
            entity.TenantId = currentTenantId;
        }

        return await base.AddAsync(entity, cancellationToken);
    }
}
