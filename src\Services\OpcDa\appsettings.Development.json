{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.Hosting.Lifetime": "Information", "BOS.Plant.Modules.OpcDa": "Debug", "Microsoft.AspNetCore.SignalR": "Debug", "System.Net.Http": "Information", "Microsoft.AspNetCore": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft.AspNetCore": "Information", "Microsoft.AspNetCore.SignalR": "Debug", "BOS.Plant.Modules.OpcDa": "Debug", "BOS.Plant.OpcDa.Service": "Debug", "System.Net.Http": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "{Timestamp:HH:mm:ss.fff} [{Level:u3}] [{SourceContext:l}] {Message:lj}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/opcda-service-dev-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "fileSizeLimitBytes": 52428800, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"}}]}, "OpcServer": {"ProgId": "Matrikon.OPC.Simulation", "ServerName": "Matrikon OPC Server for Simulation", "Host": "localhost", "ConnectionTimeout": 5000, "ReconnectInterval": 5000, "MaxReconnectAttempts": 10, "EnableSimulation": true, "Groups": []}}