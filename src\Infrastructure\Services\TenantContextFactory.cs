﻿using BOS.Plant.Core.Interfaces;
using Microsoft.AspNetCore.Http;

namespace BOS.Plant.Infrastructure.Services
{
    public class TenantContextFactory : ITenantContextFactory
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string TenantIdKey = "CurrentTenantId";

        public TenantContextFactory(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string? GetCurrentTenantId()
        {
            return _httpContextAccessor.HttpContext?.Items[TenantIdKey] as string;
        }

        public void SetCurrentTenantId(string? tenantId)
        {
            if (_httpContextAccessor.HttpContext != null)
            {
                _httpContextAccessor.HttpContext.Items[TenantIdKey] = tenantId;
            }
        }
    }
}
