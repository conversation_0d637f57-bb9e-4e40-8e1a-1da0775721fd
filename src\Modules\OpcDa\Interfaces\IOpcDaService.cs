using BOS.Plant.Modules.OpcDa.Models;
using BOS.Plant.Modules.OpcDa.DTOs;

namespace BOS.Plant.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para serviços OPC DA
/// </summary>
public interface IOpcDaService
{
    /// <summary>
    /// Evento disparado quando dados são atualizados
    /// </summary>
    event EventHandler<OpcDataUpdateEventArgs>? DataUpdated;

    /// <summary>
    /// Evento disparado quando o status da conexão muda
    /// </summary>
    event EventHandler<OpcConnectionStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// Conecta ao servidor OPC
    /// </summary>
    /// <param name="config">Configuração do servidor</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se conectado com sucesso</returns>
    Task<bool> ConnectAsync(OpcServerConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Desconecta do servidor OPC
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task DisconnectAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Adiciona um grupo de itens para monitoramento
    /// </summary>
    /// <param name="group">Grupo a ser adicionado</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se adicionado com sucesso</returns>
    Task<bool> AddGroupAsync(OpcGroup group, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um grupo de monitoramento
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removido com sucesso</returns>
    Task<bool> RemoveGroupAsync(string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se um grupo existe
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    /// <returns>True se o grupo existir</returns>
    bool GroupExists(string groupName);

    /// <summary>
    /// Adiciona um item a um grupo existente
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="item">Item a ser adicionado</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se adicionado com sucesso</returns>
    Task<bool> AddItemToGroupAsync(string groupName, OpcDataItem item, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lê o valor atual de um item
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Item com valor atual</returns>
    Task<OpcDataItem?> ReadItemAsync(string tagName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Escreve um valor em um item
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    /// <param name="value">Valor a ser escrito</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se escrito com sucesso</returns>
    Task<bool> WriteItemAsync(string tagName, object value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém o status atual da conexão
    /// </summary>
    /// <returns>Status da conexão</returns>
    OpcConnectionStatus GetConnectionStatus();

    /// <summary>
    /// Obtém todos os itens monitorados
    /// </summary>
    /// <returns>Lista de itens</returns>
    Task<IEnumerable<OpcDataItem>> GetAllItemsAsync();

    /// <summary>
    /// Obtém estatísticas do servidor
    /// </summary>
    /// <returns>Estatísticas</returns>
    Task<OpcServerStatisticsDto> GetStatisticsAsync();

    /// <summary>
    /// Inicia o monitoramento de dados
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Para o monitoramento de dados
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Argumentos do evento de atualização de dados
/// </summary>
public class OpcDataUpdateEventArgs : EventArgs
{
    public OpcDataItem Item { get; }
    public DateTime Timestamp { get; }

    public OpcDataUpdateEventArgs(OpcDataItem item)
    {
        Item = item;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// Argumentos do evento de mudança de status da conexão
/// </summary>
public class OpcConnectionStatusEventArgs : EventArgs
{
    public OpcConnectionStatus Status { get; }
    public string? Message { get; }
    public DateTime Timestamp { get; }

    public OpcConnectionStatusEventArgs(OpcConnectionStatus status, string? message = null)
    {
        Status = status;
        Message = message;
        Timestamp = DateTime.UtcNow;
    }
}
