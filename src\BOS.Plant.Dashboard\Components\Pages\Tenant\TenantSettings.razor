@page "/tenant/settings"
@inherits AdminPageBase
@inject ITenantManagementService TenantService
@inject ISnackbar Snackbar
@using BOS.Plant.Modules.Tenants.Services
@using BOS.Plant.Core.Entities
@using BOS.Plant.Dashboard.Components.Shared
@using System.ComponentModel.DataAnnotations

<PageTitle>Configurações do Tenant - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h4" Class="mb-4">Configurações do Tenant</MudText>

    @if (_loading)
    {
        <div class="d-flex justify-center pa-4">
            <MudProgressCircular Indeterminate="true" />
        </div>
    }
    else
    {
        <MudGrid>
            <!-- Informações Gerais -->
            <MudItem xs="12" md="8">
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Informações Gerais</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <EditForm Model="_settingsModel" OnValidSubmit="SaveSettings">
                            <DataAnnotationsValidator />
                            
                            <MudGrid>
                                <MudItem xs="12">
                                    <MudTextField @bind-Value="_settingsModel.Name"
                                                  Label="Nome do Tenant"
                                                  Variant="Variant.Outlined"
                                                  Required="true"
                                                  RequiredError="Nome do tenant é obrigatório"
                                                  For="@(() => _settingsModel.Name)"
                                                  Disabled="@_savingSettings" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField Label="Subdomínio"
                                                  Value="@_currentTenant?.Subdomain"
                                                  Variant="Variant.Outlined"
                                                  ReadOnly="true"
                                                  HelperText="O subdomínio não pode ser alterado" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudTextField Label="Criado em"
                                                  Value="@_currentTenant?.CreatedAt.ToString("dd/MM/yyyy HH:mm")"
                                                  Variant="Variant.Outlined"
                                                  ReadOnly="true" />
                                </MudItem>
                            </MudGrid>
                            
                            <div class="d-flex justify-end mt-4">
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Primary" 
                                           ButtonType="ButtonType.Submit"
                                           Disabled="@_savingSettings"
                                           StartIcon="@Icons.Material.Filled.Save">
                                    @if (_savingSettings)
                                    {
                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                        <MudText Class="ms-2">Salvando...</MudText>
                                    }
                                    else
                                    {
                                        <MudText>Salvar Configurações</MudText>
                                    }
                                </MudButton>
                            </div>
                        </EditForm>
                    </MudCardContent>
                </MudCard>

                <!-- Limites e Assinatura -->
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Limites e Assinatura</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        <EditForm Model="_limitsModel" OnValidSubmit="SaveLimits">
                            <DataAnnotationsValidator />
                            
                            <MudGrid>
                                <MudItem xs="12" md="6">
                                    <MudNumericField @bind-Value="_limitsModel.MaxUsers"
                                                     Label="Máximo de Usuários"
                                                     Variant="Variant.Outlined"
                                                     Min="1"
                                                     For="@(() => _limitsModel.MaxUsers)"
                                                     Disabled="@_savingLimits"
                                                     HelperText="@($"Atual: {_tenantLimits?.CurrentUsers ?? 0} usuários")" />
                                </MudItem>
                                
                                <MudItem xs="12" md="6">
                                    <MudNumericField @bind-Value="_limitsModel.MaxProjects"
                                                     Label="Máximo de Projetos"
                                                     Variant="Variant.Outlined"
                                                     Min="1"
                                                     For="@(() => _limitsModel.MaxProjects)"
                                                     Disabled="@_savingLimits"
                                                     HelperText="@($"Atual: {_tenantLimits?.CurrentProjects ?? 0} projetos")" />
                                </MudItem>
                                
                                <MudItem xs="12">
                                    <MudDatePicker @bind-Date="_subscriptionDate"
                                                   Label="Data de Expiração da Assinatura"
                                                   Variant="Variant.Outlined"
                                                   Disabled="@_savingLimits"
                                                   Clearable="true" />
                                </MudItem>
                            </MudGrid>
                            
                            <div class="d-flex justify-end mt-4">
                                <MudButton Variant="Variant.Filled" 
                                           Color="Color.Primary" 
                                           ButtonType="ButtonType.Submit"
                                           Disabled="@_savingLimits"
                                           StartIcon="@Icons.Material.Filled.Save">
                                    @if (_savingLimits)
                                    {
                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                        <MudText Class="ms-2">Salvando...</MudText>
                                    }
                                    else
                                    {
                                        <MudText>Salvar Limites</MudText>
                                    }
                                </MudButton>
                            </div>
                        </EditForm>
                    </MudCardContent>
                </MudCard>
            </MudItem>

            <!-- Estatísticas -->
            <MudItem xs="12" md="4">
                <MudCard Elevation="2" Class="mb-4">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Estatísticas</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_statistics != null)
                        {
                            <MudStack Spacing="3">
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Total de Usuários:</MudText>
                                    <MudText Typo="Typo.body2"><strong>@_statistics.TotalUsers</strong></MudText>
                                </div>
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Usuários Ativos:</MudText>
                                    <MudText Typo="Typo.body2"><strong>@_statistics.ActiveUsers</strong></MudText>
                                </div>
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Total de Projetos:</MudText>
                                    <MudText Typo="Typo.body2"><strong>@_statistics.TotalProjects</strong></MudText>
                                </div>
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Projetos Ativos:</MudText>
                                    <MudText Typo="Typo.body2"><strong>@_statistics.ActiveProjects</strong></MudText>
                                </div>
                                @if (_statistics.LastUserLogin.HasValue)
                                {
                                    <div class="d-flex justify-space-between">
                                        <MudText Typo="Typo.body2">Último Login:</MudText>
                                        <MudText Typo="Typo.body2"><strong>@_statistics.LastUserLogin.Value.ToString("dd/MM/yyyy")</strong></MudText>
                                    </div>
                                }
                            </MudStack>
                        }
                    </MudCardContent>
                </MudCard>

                <!-- Status dos Limites -->
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            <MudText Typo="Typo.h6">Status dos Limites</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent>
                        @if (_tenantLimits != null)
                        {
                            <MudStack Spacing="3">
                                @if (_tenantLimits.IsSubscriptionExpired)
                                {
                                    <MudAlert Severity="Severity.Error" Dense="true">
                                        Assinatura expirada
                                    </MudAlert>
                                }
                                else if (_tenantLimits.SubscriptionExpiresAt.HasValue)
                                {
                                    var daysToExpire = (_tenantLimits.SubscriptionExpiresAt.Value - DateTime.UtcNow).Days;
                                    <MudAlert Severity="@(daysToExpire <= 7 ? Severity.Warning : Severity.Info)" Dense="true">
                                        Expira em @daysToExpire dias
                                    </MudAlert>
                                }

                                @if (_tenantLimits.IsUserLimitReached)
                                {
                                    <MudAlert Severity="Severity.Warning" Dense="true">
                                        Limite de usuários atingido
                                    </MudAlert>
                                }

                                @if (_tenantLimits.IsProjectLimitReached)
                                {
                                    <MudAlert Severity="Severity.Warning" Dense="true">
                                        Limite de projetos atingido
                                    </MudAlert>
                                }

                                @if (!_tenantLimits.IsSubscriptionExpired && !_tenantLimits.IsUserLimitReached && !_tenantLimits.IsProjectLimitReached)
                                {
                                    <MudAlert Severity="Severity.Success" Dense="true">
                                        Todos os limites OK
                                    </MudAlert>
                                }
                            </MudStack>
                        }
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    }
</MudContainer>

@code {
    private bool _loading = true;
    private bool _savingSettings = false;
    private bool _savingLimits = false;
    
    private Tenant? _currentTenant;
    private TenantStatistics? _statistics;
    private TenantLimits? _tenantLimits;
    
    private TenantSettingsModel _settingsModel = new();
    private TenantLimitsModel _limitsModel = new();
    private DateTime? _subscriptionDate;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        _loading = true;
        try
        {
            _currentTenant = await TenantService.GetCurrentTenantAsync();
            _statistics = await TenantService.GetTenantStatisticsAsync();
            _tenantLimits = await TenantService.GetTenantLimitsAsync();

            if (_currentTenant != null)
            {
                _settingsModel.Name = _currentTenant.Name;
                _limitsModel.MaxUsers = _currentTenant.MaxUsers;
                _limitsModel.MaxProjects = _currentTenant.MaxProjects;
                _subscriptionDate = _currentTenant.SubscriptionExpiresAt;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar dados: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SaveSettings()
    {
        if (_savingSettings) return;

        _savingSettings = true;
        try
        {
            var request = new UpdateTenantSettingsRequest
            {
                Name = _settingsModel.Name
            };

            var result = await TenantService.UpdateTenantSettingsAsync(request);
            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Configurações salvas com sucesso!", Severity.Success);
                await LoadData(); // Recarregar dados
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao salvar configurações", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingSettings = false;
        }
    }

    private async Task SaveLimits()
    {
        if (_savingLimits) return;

        _savingLimits = true;
        try
        {
            var request = new UpdateTenantLimitsRequest
            {
                MaxUsers = _limitsModel.MaxUsers,
                MaxProjects = _limitsModel.MaxProjects,
                SubscriptionExpiresAt = _subscriptionDate
            };

            var result = await TenantService.UpdateTenantLimitsAsync(request);
            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Limites salvos com sucesso!", Severity.Success);
                await LoadData(); // Recarregar dados
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao salvar limites", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar limites: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingLimits = false;
        }
    }

    public class TenantSettingsModel
    {
        [Required(ErrorMessage = "Nome do tenant é obrigatório")]
        [StringLength(200, ErrorMessage = "Nome deve ter no máximo 200 caracteres")]
        public string Name { get; set; } = string.Empty;
    }

    public class TenantLimitsModel
    {
        [Range(1, int.MaxValue, ErrorMessage = "Máximo de usuários deve ser maior que 0")]
        public int? MaxUsers { get; set; }

        [Range(1, int.MaxValue, ErrorMessage = "Máximo de projetos deve ser maior que 0")]
        public int? MaxProjects { get; set; }
    }
}
