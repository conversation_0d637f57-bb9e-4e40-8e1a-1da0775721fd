using Microsoft.Extensions.Logging;
using System.Runtime.InteropServices;
using Opc;
using BOS.Plant.Modules.OpcDa.Models;

namespace BOS.Plant.Modules.OpcDa.Services;

/// <summary>
/// Serviço especializado para tratamento robusto de erros OPC
/// </summary>
public class OpcErrorHandlingService
{
    private readonly ILogger<OpcErrorHandlingService> _logger;
    private readonly Dictionary<string, int> _errorCounts = new();
    private readonly Dictionary<string, DateTime> _lastErrorTimes = new();
    private readonly object _lockObject = new();

    public OpcErrorHandlingService(ILogger<OpcErrorHandlingService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Trata exceções OPC de forma robusta
    /// </summary>
    public OpcErrorInfo HandleOpcException(Exception exception, string context, string? serverProgId = null)
    {
        lock (_lockObject)
        {
            var errorInfo = new OpcErrorInfo
            {
                Context = context,
                ServerProgId = serverProgId,
                Timestamp = DateTime.UtcNow,
                Exception = exception
            };

            switch (exception)
            {
                case Opc.ConnectFailedException connectEx:
                    errorInfo.ErrorType = OpcErrorType.ConnectionFailed;
                    errorInfo.ErrorCode = connectEx.Result.Code;
                    errorInfo.Message = $"Falha na conexão OPC: {connectEx.Message}";
                    errorInfo.Severity = OpcErrorSeverity.High;
                    errorInfo.IsRecoverable = true;
                    errorInfo.SuggestedAction = "Verificar se o servidor OPC está em execução e acessível";
                    break;

                case Exception resultEx when resultEx.GetType().Name.Contains("Result"):
                    errorInfo.ErrorType = OpcErrorType.OperationFailed;
                    errorInfo.ErrorCode = resultEx.HResult;
                    errorInfo.Message = $"Erro OPC: {resultEx.Message}";
                    errorInfo.Severity = OpcErrorSeverity.Medium;
                    errorInfo.IsRecoverable = true;
                    errorInfo.SuggestedAction = "Verificar configuração do servidor OPC";
                    break;

                case COMException comEx:
                    errorInfo.ErrorType = OpcErrorType.ComError;
                    errorInfo.ErrorCode = comEx.HResult;
                    errorInfo.Message = $"Erro COM: {comEx.Message} (HRESULT: 0x{comEx.HResult:X8})";
                    errorInfo.Severity = GetSeverityFromHResult(comEx.HResult);
                    errorInfo.IsRecoverable = IsRecoverableComError(comEx.HResult);
                    errorInfo.SuggestedAction = GetSuggestedActionFromHResult(comEx.HResult);
                    break;

                case TimeoutException timeoutEx:
                    errorInfo.ErrorType = OpcErrorType.Timeout;
                    errorInfo.Message = $"Timeout na operação OPC: {timeoutEx.Message}";
                    errorInfo.Severity = OpcErrorSeverity.Medium;
                    errorInfo.IsRecoverable = true;
                    errorInfo.SuggestedAction = "Aumentar timeout ou verificar performance do servidor";
                    break;

                default:
                    errorInfo.ErrorType = OpcErrorType.Unknown;
                    errorInfo.Message = $"Erro não categorizado: {exception.Message}";
                    errorInfo.Severity = OpcErrorSeverity.Medium;
                    errorInfo.IsRecoverable = false;
                    errorInfo.SuggestedAction = "Verificar logs detalhados e documentação";
                    break;
            }

            // Rastrear frequência de erros
            TrackErrorFrequency(errorInfo);

            // Log baseado na severidade
            LogError(errorInfo);

            return errorInfo;
        }
    }

    /// <summary>
    /// Determina se uma operação deve ser repetida baseada no erro
    /// </summary>
    public bool ShouldRetry(OpcErrorInfo errorInfo, int currentAttempt, int maxAttempts)
    {
        if (currentAttempt >= maxAttempts)
            return false;

        if (!errorInfo.IsRecoverable)
            return false;

        // Verificar se não há muitos erros recentes
        var errorKey = $"{errorInfo.Context}:{errorInfo.ServerProgId}";
        if (_errorCounts.TryGetValue(errorKey, out var count) && count > 10)
        {
            if (_lastErrorTimes.TryGetValue(errorKey, out var lastTime) && 
                DateTime.UtcNow - lastTime < TimeSpan.FromMinutes(5))
            {
                return false; // Muitos erros recentes
            }
        }

        return errorInfo.ErrorType switch
        {
            OpcErrorType.ConnectionFailed => true,
            OpcErrorType.Timeout => true,
            OpcErrorType.ServerNotAvailable => true,
            OpcErrorType.ComError => errorInfo.ErrorCode != unchecked((int)0x80040154), // REGDB_E_CLASSNOTREG
            _ => false
        };
    }

    /// <summary>
    /// Calcula delay para retry baseado no tipo de erro
    /// </summary>
    public TimeSpan GetRetryDelay(OpcErrorInfo errorInfo, int attemptNumber)
    {
        var baseDelay = errorInfo.ErrorType switch
        {
            OpcErrorType.ConnectionFailed => TimeSpan.FromSeconds(5),
            OpcErrorType.Timeout => TimeSpan.FromSeconds(2),
            OpcErrorType.ServerNotAvailable => TimeSpan.FromSeconds(10),
            OpcErrorType.ComError => TimeSpan.FromSeconds(3),
            _ => TimeSpan.FromSeconds(1)
        };

        // Exponential backoff
        return TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attemptNumber - 1));
    }

    private OpcErrorType GetErrorTypeFromResult(Opc.ResultID result)
    {
        return result.Code switch
        {
            -2147467259 => OpcErrorType.OperationFailed, // E_FAIL
            -2147024809 => OpcErrorType.InvalidArgument, // E_INVALIDARG
            -2147024882 => OpcErrorType.OutOfMemory, // E_OUTOFMEMORY
            -2147467262 => OpcErrorType.InterfaceNotSupported, // E_NOINTERFACE
            -2147220992 => OpcErrorType.ConnectionFailed, // CONNECT_E_NOCONNECTION
            _ => OpcErrorType.Unknown
        };
    }

    private OpcErrorSeverity GetSeverityFromResult(Opc.ResultID result)
    {
        return result.Code switch
        {
            -2147467259 => OpcErrorSeverity.High, // E_FAIL
            -2147024882 => OpcErrorSeverity.Critical, // E_OUTOFMEMORY
            -2147220992 => OpcErrorSeverity.High, // CONNECT_E_NOCONNECTION
            _ => OpcErrorSeverity.Medium
        };
    }

    private bool IsRecoverableResult(Opc.ResultID result)
    {
        return result.Code switch
        {
            -2147220992 => true, // CONNECT_E_NOCONNECTION
            -2147467259 => true, // E_FAIL
            -2147024809 => false, // E_INVALIDARG
            -2147024882 => false, // E_OUTOFMEMORY
            _ => false
        };
    }

    private string GetSuggestedActionFromResult(Opc.ResultID result)
    {
        return result.Code switch
        {
            -2147220992 => "Verificar conexão de rede e status do servidor", // CONNECT_E_NOCONNECTION
            -2147024809 => "Verificar parâmetros da operação", // E_INVALIDARG
            -2147024882 => "Verificar uso de memória do sistema", // E_OUTOFMEMORY
            _ => "Consultar documentação do servidor OPC"
        };
    }

    private OpcErrorSeverity GetSeverityFromHResult(int hResult)
    {
        return hResult switch
        {
            unchecked((int)0x80040154) => OpcErrorSeverity.Critical, // REGDB_E_CLASSNOTREG
            unchecked((int)0x800706BA) => OpcErrorSeverity.High,     // RPC_S_SERVER_UNAVAILABLE
            unchecked((int)0x80070005) => OpcErrorSeverity.High,     // E_ACCESSDENIED
            _ => OpcErrorSeverity.Medium
        };
    }

    private bool IsRecoverableComError(int hResult)
    {
        return hResult switch
        {
            unchecked((int)0x80040154) => false, // REGDB_E_CLASSNOTREG
            unchecked((int)0x800706BA) => true,  // RPC_S_SERVER_UNAVAILABLE
            unchecked((int)0x80070005) => false, // E_ACCESSDENIED
            _ => true
        };
    }

    private string GetSuggestedActionFromHResult(int hResult)
    {
        return hResult switch
        {
            unchecked((int)0x80040154) => "Registrar componentes OPC ou instalar servidor OPC",
            unchecked((int)0x800706BA) => "Verificar se o servidor OPC está em execução",
            unchecked((int)0x80070005) => "Verificar permissões de acesso",
            _ => "Verificar configuração COM/DCOM"
        };
    }

    private void TrackErrorFrequency(OpcErrorInfo errorInfo)
    {
        var errorKey = $"{errorInfo.Context}:{errorInfo.ServerProgId}";
        
        _errorCounts.TryGetValue(errorKey, out var count);
        _errorCounts[errorKey] = count + 1;
        _lastErrorTimes[errorKey] = errorInfo.Timestamp;
    }

    private void LogError(OpcErrorInfo errorInfo)
    {
        var logMessage = $"[{errorInfo.ErrorType}] {errorInfo.Message} | Contexto: {errorInfo.Context}";
        
        if (!string.IsNullOrEmpty(errorInfo.ServerProgId))
        {
            logMessage += $" | Servidor: {errorInfo.ServerProgId}";
        }

        if (!string.IsNullOrEmpty(errorInfo.SuggestedAction))
        {
            logMessage += $" | Ação sugerida: {errorInfo.SuggestedAction}";
        }

        switch (errorInfo.Severity)
        {
            case OpcErrorSeverity.Critical:
                _logger.LogCritical(errorInfo.Exception, logMessage);
                break;
            case OpcErrorSeverity.High:
                _logger.LogError(errorInfo.Exception, logMessage);
                break;
            case OpcErrorSeverity.Medium:
                _logger.LogWarning(errorInfo.Exception, logMessage);
                break;
            case OpcErrorSeverity.Low:
                _logger.LogInformation(errorInfo.Exception, logMessage);
                break;
        }
    }

    /// <summary>
    /// Limpa estatísticas de erro antigas
    /// </summary>
    public void CleanupOldErrors(TimeSpan maxAge)
    {
        lock (_lockObject)
        {
            var cutoffTime = DateTime.UtcNow - maxAge;
            var keysToRemove = _lastErrorTimes
                .Where(kvp => kvp.Value < cutoffTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _errorCounts.Remove(key);
                _lastErrorTimes.Remove(key);
            }
        }
    }
}
