namespace BOS.Plant.Modules.OpcDa.Models;

/// <summary>
/// Representa um nó na estrutura hierárquica de tags OPC
/// </summary>
public class OpcTagNode
{
    /// <summary>
    /// Nome do tag (sem caminho completo)
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Caminho completo do tag
    /// </summary>
    public string FullPath { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do tag
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Indica se é um nó folha (tag final)
    /// </summary>
    public bool IsLeaf { get; set; }

    /// <summary>
    /// Indica se é um branch (contém outros tags)
    /// </summary>
    public bool IsBranch { get; set; }

    /// <summary>
    /// Tipo de dados do tag
    /// </summary>
    public string DataType { get; set; } = "Unknown";

    /// <summary>
    /// Direitos de acesso do tag
    /// </summary>
    public OpcAccessRights AccessRights { get; set; }

    /// <summary>
    /// Caminho do nó pai
    /// </summary>
    public string? ParentPath { get; set; }

    /// <summary>
    /// Nível na hierarquia (0 = raiz)
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// ID do item OPC (para tags folha)
    /// </summary>
    public string? ItemId { get; set; }

    /// <summary>
    /// Indica se o tag tem filhos
    /// </summary>
    public bool HasChildren => IsBranch;

    /// <summary>
    /// Propriedades adicionais do tag
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// Timestamp da última atualização do browsing
    /// </summary>
    public DateTime LastBrowsed { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Ícone para exibição na interface
    /// </summary>
    public string Icon => IsBranch ? "folder" : GetTagIcon();

    /// <summary>
    /// Cor do ícone baseada no tipo de acesso
    /// </summary>
    public string IconColor => AccessRights switch
    {
        OpcAccessRights.Read => "primary",
        OpcAccessRights.Write => "warning",
        OpcAccessRights.ReadWrite => "success",
        _ => "default"
    };

    /// <summary>
    /// Lista de tags filhos (para navegação hierárquica)
    /// </summary>
    public List<OpcTagNode> Children { get; set; } = new();

    /// <summary>
    /// Indica se os filhos foram carregados
    /// </summary>
    public bool ChildrenLoaded { get; set; }

    /// <summary>
    /// Indica se o nó está expandido na interface
    /// </summary>
    public bool IsExpanded { get; set; }

    /// <summary>
    /// Indica se o tag está selecionado
    /// </summary>
    public bool IsSelected { get; set; }

    /// <summary>
    /// Valor atual do tag (se disponível)
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Qualidade do valor atual
    /// </summary>
    public string? CurrentQuality { get; set; }

    /// <summary>
    /// Timestamp da última atualização
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Obtém ícone baseado no tipo de dados
    /// </summary>
    private string GetTagIcon()
    {
        return DataType.ToLower() switch
        {
            "boolean" or "bool" => "toggle_on",
            "int16" or "int32" or "int64" or "integer" => "numbers",
            "single" or "double" or "decimal" or "float" => "decimal_increase",
            "string" => "text_fields",
            "datetime" => "schedule",
            _ => "label"
        };
    }

    /// <summary>
    /// Obtém texto de tooltip para o nó
    /// </summary>
    public string GetTooltip()
    {
        var tooltip = $"Nome: {Name}\nCaminho: {FullPath}\nTipo: {DataType}";
        
        if (!string.IsNullOrEmpty(Description))
            tooltip += $"\nDescrição: {Description}";
            
        if (CurrentValue != null)
            tooltip += $"\nValor: {CurrentValue}";
            
        if (LastUpdate.HasValue)
            tooltip += $"\nÚltima atualização: {LastUpdate:HH:mm:ss}";
            
        return tooltip;
    }

    /// <summary>
    /// Clona o nó (sem filhos)
    /// </summary>
    public OpcTagNode Clone()
    {
        return new OpcTagNode
        {
            Name = Name,
            FullPath = FullPath,
            Description = Description,
            IsLeaf = IsLeaf,
            IsBranch = IsBranch,
            DataType = DataType,
            AccessRights = AccessRights,
            ParentPath = ParentPath,
            Level = Level,
            CurrentValue = CurrentValue,
            CurrentQuality = CurrentQuality,
            LastUpdate = LastUpdate
        };
    }
}



/// <summary>
/// Histórico de valor de tag OPC
/// </summary>
public class OpcValueHistory
{
    public DateTime Timestamp { get; set; }
    public object? Value { get; set; }
    public string Quality { get; set; } = string.Empty;
}
