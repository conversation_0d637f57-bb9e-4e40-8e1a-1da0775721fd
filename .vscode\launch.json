{"version": "0.2.0", "configurations": [{"name": "Launch BOS.Plant.Dashboard", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/BOS.Plant.Dashboard/bin/Debug/net9.0-windows/BOS.Plant.Dashboard.dll", "args": [], "cwd": "${workspaceFolder}/src/BOS.Plant.Dashboard", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}