# Funcionalidades Avançadas OPC DA - BOS Plant System

Este documento descreve as funcionalidades avançadas implementadas para gerenciamento de servidores OPC DA e navegação de tags.

## 🚀 **Novas Funcionalidades Implementadas**

### **1. Descoberta de Servidores OPC** (`/opc/servers`)
Interface completa para descobrir, conectar e gerenciar servidores OPC DA disponíveis.

#### **Características:**
- ✅ **Descoberta Automática**: Lista servidores OPC comuns instalados
- ✅ **Verificação de Status**: Indica se cada servidor está disponível
- ✅ **Conexão Dinâmica**: Conecta a qualquer servidor disponível
- ✅ **Informações Detalhadas**: ProgID, fornecedor, versão, descrição
- ✅ **Gerenciamento de Estado**: Controla conexões ativas

#### **Servidores Suportados:**
- **<PERSON>rikon OPC Simulation Server** - Para desenvolvimento e testes
- **KEPServerEX** - Servidor industrial da PTC
- **SIMATIC NET OPC Server** - Servidor da Siemens
- **RSLinx OPC Server** - Servidor da Rockwell Automation
- **Wonderware SuiteLink** - Servidor da Wonderware

### **2. Navegador de Tags OPC** (`/opc/tags`)
Interface avançada para navegar, buscar e gerenciar tags OPC disponíveis.

#### **Características:**
- ✅ **Navegação Hierárquica**: Organização em árvore de tags
- ✅ **Busca Avançada**: Filtros por nome, tipo, descrição
- ✅ **Informações Detalhadas**: Tipo de dados, direitos de acesso, caminho
- ✅ **Adição Dinâmica**: Adiciona tags ao monitoramento em tempo real
- ✅ **Gerenciamento de Grupos**: Cria e organiza grupos de monitoramento

#### **Tipos de Tags Suportados:**
- **Random**: Valores aleatórios (Boolean, Int, Real, String)
- **Triangle Waves**: Ondas triangulares
- **Saw-toothed Waves**: Ondas dente de serra
- **Square Waves**: Ondas quadradas
- **Bucket Brigade**: Sequências ordenadas
- **WriteOnly**: Tags apenas para escrita

### **3. Gerenciamento Dinâmico de Tags**
Sistema completo para adicionar e remover tags do monitoramento.

#### **Funcionalidades:**
- ✅ **Adição Interativa**: Dialog para configurar novos tags
- ✅ **Configuração Avançada**: Taxa de atualização, descrição, unidades
- ✅ **Validação de Dados**: Valores mínimos e máximos
- ✅ **Organização em Grupos**: Agrupa tags por funcionalidade
- ✅ **Remoção Dinâmica**: Remove tags do monitoramento

### **4. Criação de Grupos Personalizados**
Interface para criar grupos customizados de monitoramento.

#### **Características:**
- ✅ **Nomes Personalizados**: Grupos com nomes descritivos
- ✅ **Taxa de Atualização**: Configuração individual por grupo
- ✅ **Sugestões Inteligentes**: Nomes pré-definidos para diferentes áreas
- ✅ **Validação**: Verificação de nomes válidos
- ✅ **Configurações Avançadas**: Grupos ativos/inativos

## 📱 **Interfaces de Usuário**

### **Gerenciar Servidores OPC** (`/opc/servers`)

```
┌─────────────────────────────────────────────────────────────┐
│ Gerenciar Servidores OPC                    [Descobrir] [X] │
├─────────────────────────────────────────────────────────────┤
│ ✅ Servidor Conectado                                       │
│ Matrikon OPC Server for Simulation                         │
│ Matrikon.OPC.Simulation.1                                  │
├─────────────────────────────────────────────────────────────┤
│ Status │ Nome do Servidor      │ ProgID        │ Ações     │
│ ✅ Disp │ Matrikon Simulation   │ Matrikon.OPC  │ [Ver Tags]│
│ ❌ Indisp│ KEPServerEX          │ KEPware.KEP   │ [Verificar]│
│ ⚪ Disp │ SIMATIC NET          │ OPC.Simatic  │ [Conectar]│
└─────────────────────────────────────────────────────────────┘
```

### **Navegador de Tags** (`/opc/tags`)

```
┌─────────────────────────────────────────────────────────────┐
│ Navegador de Tags OPC                    [Atualizar] [+Grupo]│
│ Servidor: Matrikon OPC Server for Simulation               │
├─────────────────────────────────────────────────────────────┤
│ [🔍 Buscar Tags...                              ] [Buscar] │
├─────────────────────────────────────────────────────────────┤
│ Tags Disponíveis (25)                    ✅12 Disp ⚪13 Mon │
├─────────────────────────────────────────────────────────────┤
│ St│ Nome do Tag        │ Tipo   │ Acesso │ Grupo      │ Ações│
│ ⚪ │ Random.Real8       │ Double │ Read   │ ProcessData│[Rem] │
│ ✅ │ Random.Boolean     │ Boolean│ Read   │ -          │[Add] │
│ ✅ │ Triangle.Waves.Int4│ Int32  │ Read   │ -          │[Add] │
└─────────────────────────────────────────────────────────────┘
```

### **Dialog: Adicionar Tag ao Monitoramento**

```
┌─────────────────────────────────────────────────────────────┐
│ Adicionar Tag ao Monitoramento                    [X] [✓]  │
├─────────────────────────────────────────────────────────────┤
│ Informações do Tag                                          │
│ Nome: Random.Real8                                          │
│ Tipo: Double              Acesso: Read                      │
├─────────────────────────────────────────────────────────────┤
│ Configurações de Monitoramento                             │
│ Grupo: [ProcessData ▼]    Taxa: [1000] ms                  │
│ Descrição: [Temperatura do processo...]                    │
│ Unidade: [°C]             Min: [0] Max: [100]              │
│                                          [Cancelar] [Adicionar]│
└─────────────────────────────────────────────────────────────┘
```

## 🔧 **Arquitetura Técnica**

### **Serviços Implementados:**

#### **1. IOpcDiscoveryService**
Interface principal para descoberta e gerenciamento de servidores OPC.

```csharp
public interface IOpcDiscoveryService
{
    Task<IEnumerable<OpcServerInfo>> DiscoverServersAsync(string host = "localhost");
    Task<bool> ConnectToServerAsync(OpcServerInfo serverInfo);
    Task<IEnumerable<OpcTagInfo>> BrowseTagsAsync(string path = "");
    Task<bool> AddTagToMonitoringAsync(AddTagRequest request);
    // ... outras funcionalidades
}
```

#### **2. Modelos de Dados:**

**OpcServerInfo**: Informações de servidores OPC
```csharp
public class OpcServerInfo
{
    public string ProgId { get; set; }
    public string DisplayName { get; set; }
    public string Vendor { get; set; }
    public bool IsAvailable { get; set; }
    public bool IsConnected { get; set; }
}
```

**OpcTagInfo**: Informações de tags OPC
```csharp
public class OpcTagInfo
{
    public string TagName { get; set; }
    public string DataType { get; set; }
    public OpcAccessRights AccessRights { get; set; }
    public bool IsMonitored { get; set; }
    public string? GroupName { get; set; }
}
```

### **3. Integração com SignalR**
- Comunicação em tempo real entre descoberta e monitoramento
- Eventos automáticos quando tags são adicionados/removidos
- Sincronização de estado entre interfaces

## 🎯 **Fluxo de Uso Típico**

### **1. Descobrir e Conectar a Servidor:**
1. Acessar `/opc/servers`
2. Clicar em "Descobrir Servidores"
3. Selecionar servidor disponível
4. Clicar em "Conectar"

### **2. Navegar e Adicionar Tags:**
1. Acessar `/opc/tags` (ou clicar "Ver Tags")
2. Buscar tags desejados
3. Clicar "Adicionar" no tag desejado
4. Configurar grupo e parâmetros
5. Confirmar adição

### **3. Monitorar Dados em Tempo Real:**
1. Acessar `/opc/dashboard` ou `/opc/monitor`
2. Visualizar dados atualizados automaticamente
3. Verificar estatísticas e status

### **4. Gerenciar Grupos:**
1. Criar novos grupos conforme necessário
2. Organizar tags por funcionalidade
3. Configurar taxas de atualização específicas

## 📊 **Benefícios das Novas Funcionalidades**

### **1. Flexibilidade:**
- ✅ Conecta a qualquer servidor OPC DA disponível
- ✅ Adiciona/remove tags dinamicamente
- ✅ Cria grupos personalizados

### **2. Usabilidade:**
- ✅ Interface intuitiva e responsiva
- ✅ Busca e filtros avançados
- ✅ Validação e feedback em tempo real

### **3. Produtividade:**
- ✅ Configuração rápida de monitoramento
- ✅ Descoberta automática de recursos
- ✅ Organização eficiente de dados

### **4. Escalabilidade:**
- ✅ Suporte a múltiplos servidores
- ✅ Gerenciamento de grandes volumes de tags
- ✅ Arquitetura extensível

## 🚀 **Como Testar**

### **1. Executar Aplicações:**
```bash
# Serviço OPC DA
cd src/Services/OpcDa
dotnet run

# Dashboard Blazor
cd src/BOS.Plant.Dashboard
dotnet run
```

### **2. Acessar Funcionalidades:**
- **Servidores**: `http://localhost:7129/opc/servers`
- **Tags**: `http://localhost:7129/opc/tags`
- **Dashboard**: `http://localhost:7129/opc/dashboard`
- **Monitor**: `http://localhost:7129/opc/monitor`

### **3. Testar Fluxo Completo:**
1. Descobrir servidores disponíveis
2. Conectar ao Matrikon Simulation Server
3. Navegar pelos tags disponíveis
4. Adicionar tags aos grupos de monitoramento
5. Visualizar dados em tempo real no dashboard

As novas funcionalidades transformam o sistema em uma solução completa e profissional para gerenciamento OPC DA!
