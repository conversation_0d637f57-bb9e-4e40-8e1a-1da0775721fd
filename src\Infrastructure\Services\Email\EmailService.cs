using System.Net;
using System.Net.Mail;
using System.Text;
using Microsoft.Extensions.Logging;
using BOS.Plant.Infrastructure.Services.Email.Models;

namespace BOS.Plant.Infrastructure.Services.Email;

/// <summary>
/// Implementação do serviço de e-mail usando SMTP
/// </summary>
public class EmailService : IEmailService
{
    private readonly EmailSettings _emailSettings;
    private readonly ILogger<EmailService> _logger;
    private readonly IEmailTemplateService? _templateService;

    public EmailService(
        EmailSettings emailSettings,
        ILogger<EmailService> logger,
        IEmailTemplateService? templateService = null)
    {
        _emailSettings = emailSettings;
        _logger = logger;
        _templateService = templateService;
    }

    public async Task<EmailResult> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            if (!_emailSettings.Enabled)
            {
                _logger.LogWarning("Tentativa de envio de e-mail com serviço desabilitado para {To}", to);
                return EmailResult.CreateFailure("Serviço de e-mail está desabilitado");
            }

            if (!_emailSettings.IsValid())
            {
                _logger.LogError("Configurações de e-mail inválidas");
                return EmailResult.CreateFailure("Configurações de e-mail inválidas");
            }

            using var client = CreateSmtpClient();
            using var message = CreateMailMessage(to, subject, body, isHtml);

            await client.SendMailAsync(message);

            _logger.LogInformation("E-mail enviado com sucesso para {To} com assunto '{Subject}'", to, subject);
            return EmailResult.CreateSuccess();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar e-mail para {To} com assunto '{Subject}'", to, subject);
            return EmailResult.CreateFailure($"Erro ao enviar e-mail: {ex.Message}", ex);
        }
    }

    public async Task<EmailResult> SendTemplateEmailAsync(string to, string templateName, object templateData, string subject)
    {
        try
        {
            if (_templateService == null)
            {
                _logger.LogError("Template service não está configurado");
                return EmailResult.CreateFailure("Serviço de templates não está disponível");
            }

            var templateResult = await _templateService.RenderTemplateAsync(templateName, templateData);
            if (!templateResult.Success)
            {
                return EmailResult.CreateFailure($"Erro ao processar template: {templateResult.Message}");
            }

            return await SendEmailAsync(to, subject, templateResult.Content, true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar e-mail com template {TemplateName} para {To}", templateName, to);
            return EmailResult.CreateFailure($"Erro ao enviar e-mail com template: {ex.Message}", ex);
        }
    }

    public async Task<EmailResult> SendPasswordResetEmailAsync(string to, string userName, string resetLink)
    {
        var templateData = new
        {
            UserName = userName,
            ResetLink = resetLink,
            BaseUrl = _emailSettings.BaseUrl,
            CompanyName = "Smar Hart System"
        };

        return await SendTemplateEmailAsync(to, "PasswordReset", templateData, "Recuperação de Senha - Smar Hart System");
    }

    public async Task<EmailResult> SendAccountConfirmationEmailAsync(string to, string userName, string confirmationLink)
    {
        var templateData = new
        {
            UserName = userName,
            ConfirmationLink = confirmationLink,
            BaseUrl = _emailSettings.BaseUrl,
            CompanyName = "Smar Hart System"
        };

        return await SendTemplateEmailAsync(to, "AccountConfirmation", templateData, "Confirmação de Conta - Smar Hart System");
    }

    public async Task<bool> IsConfiguredAsync()
    {
        try
        {
            return _emailSettings.IsValid() && _emailSettings.Enabled;
        }
        catch
        {
            return false;
        }
    }

    private SmtpClient CreateSmtpClient()
    {
        var client = new SmtpClient(_emailSettings.SmtpServer, _emailSettings.SmtpPort)
        {
            EnableSsl = _emailSettings.EnableSsl,
            Credentials = new NetworkCredential(_emailSettings.UserName, _emailSettings.Password),
            Timeout = _emailSettings.TimeoutSeconds * 1000
        };

        return client;
    }

    private MailMessage CreateMailMessage(string to, string subject, string body, bool isHtml)
    {
        var message = new MailMessage
        {
            From = new MailAddress(_emailSettings.FromEmail, _emailSettings.FromName),
            Subject = subject,
            Body = body,
            IsBodyHtml = isHtml,
            BodyEncoding = Encoding.UTF8,
            SubjectEncoding = Encoding.UTF8
        };

        message.To.Add(to);

        if (!string.IsNullOrWhiteSpace(_emailSettings.ReplyToEmail))
        {
            message.ReplyToList.Add(_emailSettings.ReplyToEmail);
        }

        return message;
    }
}
