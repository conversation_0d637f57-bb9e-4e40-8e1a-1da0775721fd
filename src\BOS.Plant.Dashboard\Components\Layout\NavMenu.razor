﻿
@inject AuthenticationStateProvider AuthenticationStateProvider
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization

<MudNavMenu Dense="true">
    <MudNavLink Href="/" Icon="@Icons.Material.Filled.Dashboard" IconColor="Color.Primary">
        Dashboard
    </MudNavLink>

    <MudNavGroup Title="Projetos" Icon="@Icons.Material.Filled.Work" Expanded="true">
        <MudNavLink Href="/projects" Icon="@Icons.Material.Filled.List">
            Listar Projetos
        </MudNavLink>
        <AuthorizeView Policy="Manager">
            <MudNavLink Href="/projects/create" Icon="@Icons.Material.Filled.Add">
                Novo Projeto
            </MudNavLink>
        </AuthorizeView>
    </MudNavGroup>

    <MudNavGroup Title="OPC DA" Icon="@Icons.Material.Filled.Cable" Expanded="false">
        @*<MudNavLink Href="/opc/dashboard" Icon="@Icons.Material.Filled.Dashboard">
            Dashboard OPC
        </MudNavLink>*@
        <MudNavLink Href="/opc/servers" Icon="@Icons.Material.Filled.Storage">
            Gerenciar Servidores
        </MudNavLink>
        <MudNavLink Href="/opc/tags" Icon="@Icons.Material.Filled.Label">
            Navegador de Tags
        </MudNavLink>
        <MudNavLink Href="/opc/monitor" Icon="@Icons.Material.Filled.Monitor">
            Monitor Detalhado
        </MudNavLink>
    </MudNavGroup>

    <AuthorizeView Policy="Admin">
        <MudNavGroup Title="Usuários" Icon="@Icons.Material.Filled.People">
            <MudNavLink Href="/users" Icon="@Icons.Material.Filled.List">
                Listar Usuários
            </MudNavLink>
            <MudNavLink Href="/users/create" Icon="@Icons.Material.Filled.PersonAdd">
                Novo Usuário
            </MudNavLink>
            <AuthorizeView Policy="SuperAdmin" Context="superAdminContext">
                <MudNavLink Href="/users/roles" Icon="@Icons.Material.Filled.Security">
                    Roles e Permissões
                </MudNavLink>
            </AuthorizeView>
        </MudNavGroup>
    </AuthorizeView>

    <AuthorizeView Policy="Admin">
        <MudNavGroup Title="Tenant" Icon="@Icons.Material.Filled.Business">
            <MudNavLink Href="/tenant/settings" Icon="@Icons.Material.Filled.Settings">
                Configurações
            </MudNavLink>
            <MudNavLink Href="/tenant/users" Icon="@Icons.Material.Filled.Group">
                Usuários do Tenant
            </MudNavLink>
        </MudNavGroup>
    </AuthorizeView>

    <MudDivider Class="my-2" />

    <AuthorizeView>
        <Authorized>
            <MudNavLink Href="/profile" Icon="@Icons.Material.Filled.Person">
                Meu Perfil
            </MudNavLink>
            <AuthorizeView Policy="Admin" Context="adminContext">
                <MudNavLink Href="/settings" Icon="@Icons.Material.Filled.Settings">
                    Configurações
                </MudNavLink>
            </AuthorizeView>
            <MudNavLink Href="/logout" Icon="@Icons.Material.Filled.Logout">
                Sair
            </MudNavLink>
        </Authorized>
        <NotAuthorized>
            <MudNavLink Href="/login" Icon="@Icons.Material.Filled.Login">
                Entrar
            </MudNavLink>
        </NotAuthorized>
    </AuthorizeView>
</MudNavMenu>


