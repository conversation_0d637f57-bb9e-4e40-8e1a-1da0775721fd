using Microsoft.EntityFrameworkCore;
using BOS.Plant.Core.Entities;
using BOS.Plant.Core.Interfaces;
using BOS.Plant.Infrastructure.Data;

namespace BOS.Plant.Infrastructure.Repositories;

/// <summary>
/// Repositório para tokens de reset de senha
/// </summary>
public class PasswordResetTokenRepository : TenantRepository<PasswordResetToken>, IPasswordResetTokenRepository
{
    public PasswordResetTokenRepository(ApplicationDbContext context, ITenantService tenantService) 
        : base(context, tenantService)
    {
    }

    public async Task<PasswordResetToken?> GetValidTokenAsync(string token)
    {
        return await _dbSet
            .Include(t => t.User)
            .FirstOrDefaultAsync(t => t.Token == token && 
                                     !t.IsUsed && 
                                     !t.IsDeleted && 
                                     t.ExpiresAt > DateTime.UtcNow);
    }

    public async Task<IEnumerable<PasswordResetToken>> GetValidTokensByUserIdAsync(string userId)
    {
        return await _dbSet
            .Where(t => t.UserId == userId && 
                       !t.IsUsed && 
                       !t.IsDeleted && 
                       t.ExpiresAt > DateTime.UtcNow)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> InvalidateUserTokensAsync(string userId)
    {
        var tokens = await _dbSet
            .Where(t => t.UserId == userId && 
                       !t.IsUsed && 
                       !t.IsDeleted && 
                       t.ExpiresAt > DateTime.UtcNow)
            .ToListAsync();

        foreach (var token in tokens)
        {
            token.MarkAsUsed();
        }

        await _context.SaveChangesAsync();
        return tokens.Count;
    }

    public async Task<int> CleanupExpiredTokensAsync()
    {
        var expiredTokens = await _dbSet
            .Where(t => t.ExpiresAt <= DateTime.UtcNow && !t.IsDeleted)
            .ToListAsync();

        foreach (var token in expiredTokens)
        {
            token.IsDeleted = true;
            token.DeletedAt = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return expiredTokens.Count;
    }

    public async Task<int> CountValidTokensByUserIdAsync(string userId)
    {
        return await _dbSet
            .CountAsync(t => t.UserId == userId && 
                            !t.IsUsed && 
                            !t.IsDeleted && 
                            t.ExpiresAt > DateTime.UtcNow);
    }

    public async Task<IEnumerable<PasswordResetToken>> GetTokensByIpAddressAsync(string ipAddress, int hoursBack = 24)
    {
        var cutoffTime = DateTime.UtcNow.AddHours(-hoursBack);
        
        return await _dbSet
            .Where(t => t.RequestIpAddress == ipAddress && 
                       t.CreatedAt >= cutoffTime && 
                       !t.IsDeleted)
            .OrderByDescending(t => t.CreatedAt)
            .ToListAsync();
    }
}
