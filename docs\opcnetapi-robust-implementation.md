# Implementação Robusta do OpcNetApi.Com

## Visão Geral

Este documento descreve a implementação robusta do OPC DA usando a API correta do **OpcNetApi.Com**, incluindo conexões com servidores OPC reais e tratamento avançado de erros.

## Principais Melhorias Implementadas

### 1. API Correta do OpcNetApi.Com

#### Antes (Implementação Limitada)
```csharp
// Abordagem básica e limitada
var serverType = Type.GetTypeFromProgID(config.ProgId);
var serverObject = Activator.CreateInstance(serverType);
var opcServer = (IOPCServer)serverObject;
```

#### Depois (Implementação Robusta)
```csharp
// Usando OpcNetApi.Com corretamente
var url = new Opc.URL($"opcda://{config.Host}/{config.ProgId}");
_opcServer = new Opc.Da.Server(_factory, url);

var connectData = new Opc.ConnectData(new System.Net.NetworkCredential());
if (config.ConnectionTimeout > 0)
{
    connectData.ConnectTimeout = config.ConnectionTimeout;
}

await Task.Run(() => _opcServer.Connect(connectData), cancellationToken);
```

### 2. Suporte a Conexões Remotas

A nova implementação suporta conexões tanto locais quanto remotas:

```csharp
// Conexão local
var localUrl = new Opc.URL("opcda://localhost/Matrikon.OPC.Simulation.1");

// Conexão remota
var remoteUrl = new Opc.URL("opcda://*************/KEPware.KEPServerEX.V6");

// Conexão com autenticação
var connectData = new Opc.ConnectData(new System.Net.NetworkCredential("user", "password", "domain"));
```

### 3. Tratamento Robusto de Erros

#### Sistema de Classificação de Erros
```csharp
public enum OpcErrorType
{
    ConnectionFailed,
    ServerNotAvailable,
    Timeout,
    ComError,
    OperationFailed,
    InvalidArgument,
    OutOfMemory,
    InterfaceNotSupported,
    AccessDenied,
    ItemNotFound,
    GroupNotFound,
    BadQuality,
    ConfigurationError,
    AuthenticationError,
    NetworkError
}
```

#### Tratamento Específico por Tipo de Erro
```csharp
public OpcErrorInfo HandleOpcException(Exception exception, string context, string? serverProgId = null)
{
    switch (exception)
    {
        case Opc.ConnectFailedException connectEx:
            return new OpcErrorInfo
            {
                ErrorType = OpcErrorType.ConnectionFailed,
                IsRecoverable = true,
                SuggestedAction = "Verificar se o servidor OPC está em execução"
            };
            
        case COMException comEx when comEx.HResult == unchecked((int)0x80040154):
            return new OpcErrorInfo
            {
                ErrorType = OpcErrorType.ComError,
                IsRecoverable = false,
                SuggestedAction = "Registrar componentes OPC ou instalar servidor OPC"
            };
    }
}
```

### 4. Gerenciamento Avançado de Grupos

#### Criação de Grupos com OpcNetApi
```csharp
public async Task<bool> AddGroupAsync(OpcGroup group, CancellationToken cancellationToken = default)
{
    var groupState = new Opc.Da.GroupState
    {
        Name = group.Name,
        Active = group.IsActive,
        UpdateRate = group.UpdateRate,
        DeadBand = 0.0f,
        LocaleID = 0
    };

    var opcGroup = _opcServer.CreateGroup(groupState);
    _groups[group.Name] = opcGroup;
    
    // Adicionar itens ao grupo
    if (group.Items.Any())
    {
        await AddItemsToGroupAsync(group.Name, group.Items, cancellationToken);
    }
}
```

#### Adição de Itens com Tratamento de Resultados
```csharp
private Task AddItemsToGroupAsync(string groupName, List<OpcDataItem> items, CancellationToken cancellationToken)
{
    var itemDefinitions = items.Select(item => new Opc.Da.Item
    {
        ItemName = item.TagName,
        ClientHandle = item.TagName.GetHashCode(),
        Active = true,
        ReqType = null
    }).ToArray();

    var results = group.CreateItems(itemDefinitions);

    for (int i = 0; i < results.Length; i++)
    {
        var result = results[i];
        var item = items[i];

        if (result.Result.Succeeded())
        {
            item.ServerHandle = result.ServerHandle;
            item.CanonicalDataType = result.CanonicalDataType;
            item.AccessRights = ConvertAccessRights(result.AccessRights);
            _items[item.TagName] = item;
        }
        else
        {
            _logger.LogWarning("Falha ao adicionar item {TagName}: {Error}", 
                item.TagName, result.Result);
        }
    }
}
```

### 5. Browsing Avançado de Tags

#### Navegação do Address Space
```csharp
private Opc.Da.BrowseElement[] BrowseServerAddressSpace(Opc.Da.Server server, string? parentPath)
{
    var filters = new Opc.Da.BrowseFilters
    {
        BrowseFilter = Opc.Da.browseFilter.all,
        ElementNameFilter = "",
        VendorFilter = "",
        ReturnAllProperties = false,
        ReturnPropertyValues = false,
        PropertyIDs = null
    };

    var position = new Opc.Da.BrowsePosition(parentPath, filters);
    var elements = server.Browse(position);
    
    return elements ?? new Opc.Da.BrowseElement[0];
}
```

### 6. Sistema de Retry Inteligente

#### Retry com Análise de Erro
```csharp
public bool ShouldRetry(OpcErrorInfo errorInfo, int currentAttempt, int maxAttempts)
{
    if (currentAttempt >= maxAttempts || !errorInfo.IsRecoverable)
        return false;

    return errorInfo.ErrorType switch
    {
        OpcErrorType.ConnectionFailed => true,
        OpcErrorType.Timeout => true,
        OpcErrorType.ServerNotAvailable => true,
        OpcErrorType.ComError => errorInfo.ErrorCode != unchecked((int)0x80040154),
        _ => false
    };
}
```

#### Delay Inteligente para Retry
```csharp
public TimeSpan GetRetryDelay(OpcErrorInfo errorInfo, int attemptNumber)
{
    var baseDelay = errorInfo.ErrorType switch
    {
        OpcErrorType.ConnectionFailed => TimeSpan.FromSeconds(5),
        OpcErrorType.Timeout => TimeSpan.FromSeconds(2),
        OpcErrorType.ServerNotAvailable => TimeSpan.FromSeconds(10),
        OpcErrorType.ComError => TimeSpan.FromSeconds(3),
        _ => TimeSpan.FromSeconds(1)
    };

    // Exponential backoff
    return TimeSpan.FromMilliseconds(baseDelay.TotalMilliseconds * Math.Pow(2, attemptNumber - 1));
}
```

## Benefícios da Nova Implementação

### 1. **Compatibilidade Ampliada**
- Suporte a servidores OPC DA 1.0, 2.0 e 3.0
- Conexões locais e remotas
- Autenticação com credenciais de rede

### 2. **Robustez Operacional**
- Tratamento específico para cada tipo de erro
- Retry inteligente baseado no tipo de erro
- Recuperação automática de conexões perdidas

### 3. **Melhor Observabilidade**
- Logging detalhado por severidade de erro
- Estatísticas de erro e performance
- Sugestões automáticas de ação corretiva

### 4. **Performance Otimizada**
- Cache inteligente de conexões
- Gerenciamento eficiente de recursos
- Cleanup automático de recursos não utilizados

## Configuração e Uso

### 1. Configuração Básica
```json
{
  "OpcDa": {
    "ConnectionTimeout": 30000,
    "DefaultUpdateRate": 1000,
    "MaxReconnectAttempts": 3,
    "ReconnectInterval": 5000,
    "EnableDetailedLogging": true
  }
}
```

### 2. Exemplo de Uso
```csharp
// Injeção de dependência
services.AddOpcDaServices(configuration);

// Uso do serviço
var opcService = serviceProvider.GetRequiredService<IOpcDaService>();

var config = new OpcServerConfig
{
    ProgId = "Matrikon.OPC.Simulation.1",
    Host = "localhost", // ou IP remoto
    ConnectionTimeout = 30000,
    Groups = new List<OpcGroup>
    {
        new OpcGroup
        {
            Name = "ProcessData",
            IsActive = true,
            UpdateRate = 1000,
            Items = new List<OpcDataItem>
            {
                new OpcDataItem { TagName = "Random.Real8", DataType = "Double" },
                new OpcDataItem { TagName = "Random.Boolean", DataType = "Boolean" }
            }
        }
    }
};

var connected = await opcService.ConnectAsync(config);
if (connected)
{
    var value = await opcService.ReadItemAsync("Random.Real8");
    Console.WriteLine($"Valor lido: {value?.Value}");
}
```

## Testes e Validação

A implementação inclui testes abrangentes que validam:

1. **Conexão com servidores reais**
2. **Descoberta de servidores usando OpcNetApi**
3. **Navegação de tags com browsing real**
4. **Tratamento robusto de erros**
5. **Sistema de retry inteligente**
6. **Workflow completo de integração**

Execute os testes com:
```bash
dotnet test src/Modules/OpcDa/Tests/OpcNetApiImplementationTests.cs
```

## Conclusão

A nova implementação fornece uma base sólida e robusta para comunicação OPC DA, utilizando as melhores práticas e a API correta do OpcNetApi.Com. Isso garante compatibilidade com servidores OPC reais, tratamento robusto de erros e operação confiável em ambientes de produção.
