using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using BOS.Plant.Modules.OpcDa.Models;

namespace BOS.Plant.Modules.OpcDa.Services;

/// <summary>
/// Serviço para coleta e análise de métricas de performance OPC
/// </summary>
public class OpcPerformanceMetricsService : IDisposable
{
    private readonly ILogger<OpcPerformanceMetricsService> _logger;
    private readonly ConcurrentDictionary<string, OpcOperationMetrics> _operationMetrics = new();
    private readonly ConcurrentDictionary<string, OpcConnectionMetrics> _connectionMetrics = new();
    private readonly ConcurrentQueue<OpcPerformanceEvent> _performanceEvents = new();
    private readonly System.Threading.Timer _metricsTimer;
    private readonly object _metricsLock = new();
    private readonly Stopwatch _serviceUptime = Stopwatch.StartNew();

    public OpcPerformanceMetricsService(ILogger<OpcPerformanceMetricsService> logger)
    {
        _logger = logger;
        
        // Timer para processar métricas a cada 30 segundos
        _metricsTimer = new System.Threading.Timer(ProcessMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    /// <summary>
    /// Inicia medição de uma operação OPC
    /// </summary>
    public IDisposable StartOperation(string operationType, string? serverProgId = null, string? context = null)
    {
        return new OpcOperationMeasurement(this, operationType, serverProgId, context);
    }

    /// <summary>
    /// Registra uma operação OPC concluída
    /// </summary>
    public void RecordOperation(string operationType, TimeSpan duration, bool success, string? serverProgId = null, string? errorMessage = null)
    {
        try
        {
            var key = $"{operationType}:{serverProgId ?? "unknown"}";
            
            var metrics = _operationMetrics.GetOrAdd(key, _ => new OpcOperationMetrics
            {
                OperationType = operationType,
                ServerProgId = serverProgId
            });

            lock (metrics)
            {
                metrics.TotalOperations++;
                metrics.TotalDuration += duration;
                
                if (success)
                {
                    metrics.SuccessfulOperations++;
                }
                else
                {
                    metrics.FailedOperations++;
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        metrics.RecentErrors.Add(new OpcErrorRecord
                        {
                            Timestamp = DateTime.UtcNow,
                            ErrorMessage = errorMessage
                        });
                        
                        // Manter apenas os últimos 10 erros
                        while (metrics.RecentErrors.Count > 10)
                        {
                            metrics.RecentErrors.RemoveAt(0);
                        }
                    }
                }

                // Atualizar estatísticas
                metrics.AverageDuration = TimeSpan.FromMilliseconds(metrics.TotalDuration.TotalMilliseconds / metrics.TotalOperations);
                metrics.SuccessRate = (double)metrics.SuccessfulOperations / metrics.TotalOperations * 100;
                metrics.LastOperationTime = DateTime.UtcNow;

                // Atualizar min/max
                if (duration < metrics.MinDuration || metrics.MinDuration == TimeSpan.Zero)
                    metrics.MinDuration = duration;
                if (duration > metrics.MaxDuration)
                    metrics.MaxDuration = duration;
            }

            // Registrar evento de performance
            _performanceEvents.Enqueue(new OpcPerformanceEvent
            {
                Timestamp = DateTime.UtcNow,
                OperationType = operationType,
                ServerProgId = serverProgId,
                Duration = duration,
                Success = success,
                ErrorMessage = errorMessage
            });

            // Limitar fila de eventos
            while (_performanceEvents.Count > 1000)
            {
                _performanceEvents.TryDequeue(out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao registrar métricas de operação: {OperationType}", operationType);
        }
    }

    /// <summary>
    /// Registra métricas de conexão
    /// </summary>
    public void RecordConnection(string serverProgId, string host, bool connected, TimeSpan connectionTime, string? errorMessage = null)
    {
        try
        {
            var key = $"{host}:{serverProgId}";
            
            var metrics = _connectionMetrics.GetOrAdd(key, _ => new OpcConnectionMetrics
            {
                ServerProgId = serverProgId,
                Host = host
            });

            lock (metrics)
            {
                metrics.TotalConnectionAttempts++;
                
                if (connected)
                {
                    metrics.SuccessfulConnections++;
                    metrics.LastSuccessfulConnection = DateTime.UtcNow;
                    metrics.IsCurrentlyConnected = true;
                    
                    // Atualizar estatísticas de tempo de conexão
                    metrics.TotalConnectionTime += connectionTime;
                    metrics.AverageConnectionTime = TimeSpan.FromMilliseconds(
                        metrics.TotalConnectionTime.TotalMilliseconds / metrics.SuccessfulConnections);
                    
                    if (connectionTime < metrics.MinConnectionTime || metrics.MinConnectionTime == TimeSpan.Zero)
                        metrics.MinConnectionTime = connectionTime;
                    if (connectionTime > metrics.MaxConnectionTime)
                        metrics.MaxConnectionTime = connectionTime;
                }
                else
                {
                    metrics.FailedConnections++;
                    metrics.LastFailedConnection = DateTime.UtcNow;
                    metrics.IsCurrentlyConnected = false;
                    
                    if (!string.IsNullOrEmpty(errorMessage))
                    {
                        metrics.RecentConnectionErrors.Add(new OpcErrorRecord
                        {
                            Timestamp = DateTime.UtcNow,
                            ErrorMessage = errorMessage
                        });
                        
                        // Manter apenas os últimos 5 erros
                        while (metrics.RecentConnectionErrors.Count > 5)
                        {
                            metrics.RecentConnectionErrors.RemoveAt(0);
                        }
                    }
                }

                metrics.ConnectionSuccessRate = (double)metrics.SuccessfulConnections / metrics.TotalConnectionAttempts * 100;
                metrics.LastAttempt = DateTime.UtcNow;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao registrar métricas de conexão: {ServerProgId}@{Host}", serverProgId, host);
        }
    }

    /// <summary>
    /// Registra desconexão
    /// </summary>
    public void RecordDisconnection(string serverProgId, string host, TimeSpan connectionDuration)
    {
        try
        {
            var key = $"{host}:{serverProgId}";
            
            if (_connectionMetrics.TryGetValue(key, out var metrics))
            {
                lock (metrics)
                {
                    metrics.IsCurrentlyConnected = false;
                    metrics.TotalConnectedTime += connectionDuration;
                    metrics.DisconnectionCount++;
                    metrics.LastDisconnection = DateTime.UtcNow;
                    
                    if (metrics.SuccessfulConnections > 0)
                    {
                        metrics.AverageSessionDuration = TimeSpan.FromMilliseconds(
                            metrics.TotalConnectedTime.TotalMilliseconds / metrics.SuccessfulConnections);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao registrar desconexão: {ServerProgId}@{Host}", serverProgId, host);
        }
    }

    /// <summary>
    /// Obtém métricas de operações
    /// </summary>
    public IEnumerable<OpcOperationMetrics> GetOperationMetrics()
    {
        return _operationMetrics.Values.ToList();
    }

    /// <summary>
    /// Obtém métricas de conexões
    /// </summary>
    public IEnumerable<OpcConnectionMetrics> GetConnectionMetrics()
    {
        return _connectionMetrics.Values.ToList();
    }

    /// <summary>
    /// Obtém resumo geral de performance
    /// </summary>
    public OpcPerformanceSummary GetPerformanceSummary()
    {
        lock (_metricsLock)
        {
            var operationMetrics = _operationMetrics.Values.ToList();
            var connectionMetrics = _connectionMetrics.Values.ToList();
            
            return new OpcPerformanceSummary
            {
                ServiceUptime = _serviceUptime.Elapsed,
                TotalOperations = operationMetrics.Sum(m => m.TotalOperations),
                TotalSuccessfulOperations = operationMetrics.Sum(m => m.SuccessfulOperations),
                TotalFailedOperations = operationMetrics.Sum(m => m.FailedOperations),
                OverallSuccessRate = operationMetrics.Any() ? 
                    operationMetrics.Average(m => m.SuccessRate) : 0,
                AverageOperationDuration = operationMetrics.Any() ? 
                    TimeSpan.FromMilliseconds(operationMetrics.Average(m => m.AverageDuration.TotalMilliseconds)) : TimeSpan.Zero,
                TotalConnections = connectionMetrics.Sum(m => m.TotalConnectionAttempts),
                ActiveConnections = connectionMetrics.Count(m => m.IsCurrentlyConnected),
                AverageConnectionTime = connectionMetrics.Any() ? 
                    TimeSpan.FromMilliseconds(connectionMetrics.Average(m => m.AverageConnectionTime.TotalMilliseconds)) : TimeSpan.Zero,
                GeneratedAt = DateTime.UtcNow
            };
        }
    }

    /// <summary>
    /// Obtém eventos de performance recentes
    /// </summary>
    public IEnumerable<OpcPerformanceEvent> GetRecentEvents(int count = 100)
    {
        return _performanceEvents.TakeLast(count).ToList();
    }

    /// <summary>
    /// Limpa métricas antigas
    /// </summary>
    public void ClearOldMetrics(TimeSpan maxAge)
    {
        try
        {
            var cutoffTime = DateTime.UtcNow - maxAge;
            
            // Limpar eventos antigos
            var eventsToKeep = new List<OpcPerformanceEvent>();
            while (_performanceEvents.TryDequeue(out var evt))
            {
                if (evt.Timestamp > cutoffTime)
                {
                    eventsToKeep.Add(evt);
                }
            }
            
            foreach (var evt in eventsToKeep)
            {
                _performanceEvents.Enqueue(evt);
            }

            // Limpar erros antigos das métricas
            foreach (var metrics in _operationMetrics.Values)
            {
                lock (metrics)
                {
                    metrics.RecentErrors.RemoveAll(e => e.Timestamp < cutoffTime);
                }
            }

            foreach (var metrics in _connectionMetrics.Values)
            {
                lock (metrics)
                {
                    metrics.RecentConnectionErrors.RemoveAll(e => e.Timestamp < cutoffTime);
                }
            }

            _logger.LogDebug("Métricas antigas limpas. Cutoff: {CutoffTime}", cutoffTime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao limpar métricas antigas");
        }
    }

    private void ProcessMetrics(object? state)
    {
        try
        {
            var summary = GetPerformanceSummary();
            
            _logger.LogInformation("Métricas OPC - Operações: {TotalOps} (Sucesso: {SuccessRate:F1}%), " +
                                 "Conexões Ativas: {ActiveConns}, Uptime: {Uptime}",
                summary.TotalOperations, summary.OverallSuccessRate, 
                summary.ActiveConnections, summary.ServiceUptime);

            // Limpar métricas antigas (mais de 1 hora)
            ClearOldMetrics(TimeSpan.FromHours(1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar métricas");
        }
    }

    public void Dispose()
    {
        try
        {
            _metricsTimer?.Dispose();
            _serviceUptime?.Stop();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante dispose do OpcPerformanceMetricsService");
        }
    }
}

/// <summary>
/// Classe para medição automática de operações
/// </summary>
public class OpcOperationMeasurement : IDisposable
{
    private readonly OpcPerformanceMetricsService _metricsService;
    private readonly string _operationType;
    private readonly string? _serverProgId;
    private readonly string? _context;
    private readonly Stopwatch _stopwatch;
    private bool _disposed = false;

    public OpcOperationMeasurement(OpcPerformanceMetricsService metricsService, string operationType, string? serverProgId, string? context)
    {
        _metricsService = metricsService;
        _operationType = operationType;
        _serverProgId = serverProgId;
        _context = context;
        _stopwatch = Stopwatch.StartNew();
    }

    public void Complete(bool success = true, string? errorMessage = null)
    {
        if (!_disposed)
        {
            _stopwatch.Stop();
            _metricsService.RecordOperation(_operationType, _stopwatch.Elapsed, success, _serverProgId, errorMessage);
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Complete(true);
    }
}
