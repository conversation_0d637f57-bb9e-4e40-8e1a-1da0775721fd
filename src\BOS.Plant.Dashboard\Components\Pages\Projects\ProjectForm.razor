@page "/projects/create"
@page "/projects/edit/{ProjectId}"
@inject IProjectService ProjectService
@inject IUserManagementService UserService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar

<PageTitle>@(_isEdit ? "Editar Projeto" : "Novo Projeto") - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">@(_isEdit ? "Editar Projeto" : "Novo Projeto")</MudText>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <EditForm Model="_model" OnValidSubmit="SaveProject">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12" md="8">
                        <MudTextField @bind-Value="_model.Name" 
                                     Label="Nome do Projeto" 
                                     Variant="Variant.Outlined" 
                                     Required="true"
                                     For="@(() => _model.Name)" />
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudSelect @bind-Value="_model.OwnerId" 
                                  Label="Proprietário" 
                                  Variant="Variant.Outlined" 
                                  Required="true"
                                  For="@(() => _model.OwnerId)">
                            @foreach (var user in _users)
                            {
                                <MudSelectItem Value="@user.Id">@user.FullName</MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_model.Description" 
                                     Label="Descrição" 
                                     Variant="Variant.Outlined" 
                                     Lines="3"
                                     For="@(() => _model.Description)" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudDatePicker @bind-Date="_startDate" 
                                      Label="Data de Início" 
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudDatePicker @bind-Date="_endDate" 
                                      Label="Data de Fim" 
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudSelect @bind-Value="_model.Status" 
                                  Label="Status" 
                                  Variant="Variant.Outlined">
                            <MudSelectItem Value="ProjectStatus.Planning">Planejamento</MudSelectItem>
                            <MudSelectItem Value="ProjectStatus.InProgress">Em Andamento</MudSelectItem>
                            <MudSelectItem Value="ProjectStatus.OnHold">Pausado</MudSelectItem>
                            <MudSelectItem Value="ProjectStatus.Completed">Concluído</MudSelectItem>
                            <MudSelectItem Value="ProjectStatus.Cancelled">Cancelado</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudSelect @bind-Value="_model.Priority" 
                                  Label="Prioridade" 
                                  Variant="Variant.Outlined">
                            <MudSelectItem Value="ProjectPriority.Low">Baixa</MudSelectItem>
                            <MudSelectItem Value="ProjectPriority.Medium">Média</MudSelectItem>
                            <MudSelectItem Value="ProjectPriority.High">Alta</MudSelectItem>
                            <MudSelectItem Value="ProjectPriority.Critical">Crítica</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudNumericField @bind-Value="_model.Budget"
                                        Label="Orçamento" 
                                        Variant="Variant.Outlined" 
                                        Format="C"
                                        Culture="@System.Globalization.CultureInfo.GetCultureInfo("pt-BR")" />
                    </MudItem>
                    
                    <MudItem xs="12">
                        <MudTextField @bind-Value="_tagsText" 
                                     Label="Tags (separadas por vírgula)" 
                                     Variant="Variant.Outlined" 
                                     Placeholder="tag1, tag2, tag3" />
                    </MudItem>
                </MudGrid>
                
                <div class="d-flex justify-end gap-2 mt-4">
                    <MudButton Variant="Variant.Text" OnClick="GoBack">
                        Cancelar
                    </MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                              Variant="Variant.Filled" 
                              Color="Color.Primary" 
                              Disabled="_saving">
                        @if (_saving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">Salvando...</MudText>
                        }
                        else
                        {
                            <MudText>@(_isEdit ? "Atualizar" : "Criar") Projeto</MudText>
                        }
                    </MudButton>
                </div>
            </EditForm>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    [Parameter] public string? ProjectId { get; set; }

    private bool _isEdit => !string.IsNullOrEmpty(ProjectId);
    private bool _saving = false;
    private ProjectFormModel _model = new();
    private List<ApplicationUser> _users = new();
    private DateTime? _startDate;
    private DateTime? _endDate;
    private string _tagsText = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        
        if (_isEdit && !string.IsNullOrEmpty(ProjectId))
        {
            await LoadProject();
        }
    }

    private async Task LoadUsers()
    {
        try
        {
            _users = (await UserService.GetUsersAsync()).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar usuários: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadProject()
    {
        try
        {
            var project = await ProjectService.GetProjectByIdAsync(ProjectId!);
            if (project != null)
            {
                _model = new ProjectFormModel
                {
                    Name = project.Name,
                    Description = project.Description,
                    OwnerId = project.OwnerId,
                    Status = project.Status,
                    Priority = project.Priority,
                    Budget = project.Budget
                };
                
                _startDate = project.StartDate;
                _endDate = project.EndDate;
                
                if (!string.IsNullOrEmpty(project.Tags))
                {
                    try
                    {
                        var tags = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.Tags);
                        _tagsText = string.Join(", ", tags ?? new List<string>());
                    }
                    catch
                    {
                        _tagsText = string.Empty;
                    }
                }
            }
            else
            {
                Snackbar.Add("Projeto não encontrado", Severity.Error);
                Navigation.NavigateTo("/projects");
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar projeto: {ex.Message}", Severity.Error);
            Navigation.NavigateTo("/projects");
        }
    }

    private async Task SaveProject()
    {
        _saving = true;
        try
        {
            var tags = _tagsText.Split(',', StringSplitOptions.RemoveEmptyEntries)
                               .Select(t => t.Trim())
                               .Where(t => !string.IsNullOrEmpty(t))
                               .ToList();

            if (_isEdit)
            {
                var updateRequest = new UpdateProjectRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    StartDate = _startDate,
                    EndDate = _endDate,
                    Priority = _model.Priority,
                    Budget = _model.Budget,
                    Tags = tags
                };

                var result = await ProjectService.UpdateProjectAsync(ProjectId!, updateRequest);
                if (result.Success)
                {
                    Snackbar.Add("Projeto atualizado com sucesso", Severity.Success);
                    Navigation.NavigateTo("/projects");
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao atualizar projeto", Severity.Error);
                }
            }
            else
            {
                var createRequest = new CreateProjectRequest
                {
                    Name = _model.Name,
                    Description = _model.Description,
                    OwnerId = _model.OwnerId,
                    StartDate = _startDate,
                    EndDate = _endDate,
                    Status = _model.Status,
                    Priority = _model.Priority,
                    Budget = _model.Budget,
                    Tags = tags
                };

                var result = await ProjectService.CreateProjectAsync(createRequest);
                if (result.Success)
                {
                    Snackbar.Add("Projeto criado com sucesso", Severity.Success);
                    Navigation.NavigateTo("/projects");
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao criar projeto", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar projeto: {ex.Message}", Severity.Error);
        }
        finally
        {
            _saving = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/projects");
    }

    public class ProjectFormModel
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string OwnerId { get; set; } = string.Empty;
        public ProjectStatus Status { get; set; } = ProjectStatus.Planning;
        public ProjectPriority Priority { get; set; } = ProjectPriority.Medium;
        public decimal? Budget { get; set; }
    }
}
