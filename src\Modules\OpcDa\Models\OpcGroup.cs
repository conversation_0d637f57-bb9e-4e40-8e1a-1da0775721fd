using System.ComponentModel.DataAnnotations;

namespace BOS.Plant.Modules.OpcDa.Models;

/// <summary>
/// Representa um grupo OPC DA
/// </summary>
public class OpcGroup
{
    /// <summary>
    /// Nome do grupo
    /// </summary>
    [Required]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Indica se o grupo está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Taxa de atualização em milissegundos
    /// </summary>
    public int UpdateRate { get; set; } = 1000;

    /// <summary>
    /// Itens do grupo
    /// </summary>
    public List<OpcDataItem> Items { get; set; } = new();

    /// <summary>
    /// Timestamp da última atualização
    /// </summary>
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Número de itens no grupo
    /// </summary>
    public int ItemCount => Items.Count;

    /// <summary>
    /// Número de itens ativos no grupo
    /// </summary>
    public int ActiveItemCount => Items.Count(i => i.IsActive);
}

/// <summary>
/// Configuração do servidor OPC
/// </summary>
public class OpcServerConfig
{
    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    [Required]
    public string ProgId { get; set; } = string.Empty;

    /// <summary>
    /// Nome do servidor (opcional)
    /// </summary>
    public string? ServerName { get; set; }

    /// <summary>
    /// Host do servidor (para conexões remotas)
    /// </summary>
    public string Host { get; set; } = "localhost";

    /// <summary>
    /// Timeout de conexão em milissegundos
    /// </summary>
    public int ConnectionTimeout { get; set; } = 5000;

    /// <summary>
    /// Intervalo de reconexão em milissegundos
    /// </summary>
    public int ReconnectInterval { get; set; } = 10000;

    /// <summary>
    /// Número máximo de tentativas de reconexão
    /// </summary>
    public int MaxReconnectAttempts { get; set; } = 5;

    /// <summary>
    /// Habilita o modo de simulação para testes sem servidor OPC real
    /// </summary>
    public bool EnableSimulation { get; set; } = false;

    /// <summary>
    /// Grupos configurados
    /// </summary>
    public List<OpcGroup> Groups { get; set; } = new();
}
