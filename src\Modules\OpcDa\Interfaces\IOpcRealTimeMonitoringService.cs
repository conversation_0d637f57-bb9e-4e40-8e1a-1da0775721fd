using BOS.Plant.Modules.OpcDa.Models;

namespace BOS.Plant.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para monitoramento em tempo real de tags OPC
/// </summary>
public interface IOpcRealTimeMonitoringService
{
    /// <summary>
    /// Evento disparado quando um valor de tag muda
    /// </summary>
    event EventHandler<OpcValueChangedEventArgs>? ValueChanged;

    /// <summary>
    /// Evento disparado quando o status de conexão muda
    /// </summary>
    event EventHandler<OpcConnectionStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// Evento disparado quando as estatísticas são atualizadas
    /// </summary>
    event EventHandler<OpcMonitoringStatistics>? StatisticsUpdated;

    /// <summary>
    /// Inicia monitoramento de tags
    /// </summary>
    /// <param name="config">Configuração do monitoramento</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>ID da sessão de monitoramento</returns>
    Task<string> StartMonitoringAsync(OpcMonitoringConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Para monitoramento de uma sessão
    /// </summary>
    /// <param name="sessionId">ID da sessão</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task StopMonitoringAsync(string sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adiciona tags a uma sessão de monitoramento
    /// </summary>
    /// <param name="sessionId">ID da sessão</param>
    /// <param name="tagPaths">Caminhos dos tags a adicionar</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task AddTagsToMonitoringAsync(string sessionId, List<string> tagPaths, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove tags de uma sessão de monitoramento
    /// </summary>
    /// <param name="sessionId">ID da sessão</param>
    /// <param name="tagPaths">Caminhos dos tags a remover</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task RemoveTagsFromMonitoringAsync(string sessionId, List<string> tagPaths, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas de monitoramento
    /// </summary>
    /// <param name="sessionId">ID da sessão (null para estatísticas globais)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas de monitoramento</returns>
    Task<OpcMonitoringStatistics> GetMonitoringStatisticsAsync(string? sessionId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista todas as sessões ativas
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de informações das sessões ativas</returns>
    Task<List<OpcMonitoringSessionInfo>> GetActiveSessionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lê valores atuais de tags específicos
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="tagPaths">Caminhos dos tags a ler</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de valores dos tags</returns>
    Task<List<OpcTagValue>> ReadTagValuesAsync(string serverProgId, List<string> tagPaths, CancellationToken cancellationToken = default);
}
