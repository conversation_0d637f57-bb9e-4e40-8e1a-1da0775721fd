using BOS.Plant.Infrastructure.Services.Email.Models;

namespace BOS.Plant.Infrastructure.Services.Email;

/// <summary>
/// Interface para serviços de envio de e-mail
/// </summary>
public interface IEmailService
{
    /// <summary>
    /// Envia um e-mail simples
    /// </summary>
    /// <param name="to">Destinatário</param>
    /// <param name="subject">Assunto</param>
    /// <param name="body">Corpo do e-mail</param>
    /// <param name="isHtml">Indica se o corpo é HTML</param>
    /// <returns>Resultado do envio</returns>
    Task<EmailResult> SendEmailAsync(string to, string subject, string body, bool isHtml = true);

    /// <summary>
    /// Envia um e-mail usando template
    /// </summary>
    /// <param name="to">Destinatário</param>
    /// <param name="templateName">Nome do template</param>
    /// <param name="templateData">Dados para o template</param>
    /// <param name="subject">Assunto do e-mail</param>
    /// <returns>Resultado do envio</returns>
    Task<EmailResult> SendTemplateEmailAsync(string to, string templateName, object templateData, string subject);

    /// <summary>
    /// Envia e-mail de recuperação de senha
    /// </summary>
    /// <param name="to">E-mail do destinatário</param>
    /// <param name="userName">Nome do usuário</param>
    /// <param name="resetLink">Link para reset da senha</param>
    /// <returns>Resultado do envio</returns>
    Task<EmailResult> SendPasswordResetEmailAsync(string to, string userName, string resetLink);

    /// <summary>
    /// Envia e-mail de confirmação de conta
    /// </summary>
    /// <param name="to">E-mail do destinatário</param>
    /// <param name="userName">Nome do usuário</param>
    /// <param name="confirmationLink">Link de confirmação</param>
    /// <returns>Resultado do envio</returns>
    Task<EmailResult> SendAccountConfirmationEmailAsync(string to, string userName, string confirmationLink);

    /// <summary>
    /// Verifica se o serviço de e-mail está configurado corretamente
    /// </summary>
    /// <returns>True se configurado corretamente</returns>
    Task<bool> IsConfiguredAsync();
}
