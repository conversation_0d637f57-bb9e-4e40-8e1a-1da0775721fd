using BOS.Plant.Core.Entities;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Interface para serviço de tokens de reset de senha
/// </summary>
public interface IPasswordResetTokenService
{
    /// <summary>
    /// Gera um novo token de reset para um usuário
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <param name="ipAddress">Endereço IP da solicitação</param>
    /// <param name="userAgent">User Agent da solicitação</param>
    /// <returns>Token gerado</returns>
    Task<PasswordResetToken> GenerateTokenAsync(string userId, string? ipAddress = null, string? userAgent = null);

    /// <summary>
    /// Valida um token de reset
    /// </summary>
    /// <param name="token">Token a ser validado</param>
    /// <returns>Token se válido, null caso contrário</returns>
    Task<PasswordResetToken?> ValidateTokenAsync(string token);

    /// <summary>
    /// Consome um token (marca como usado)
    /// </summary>
    /// <param name="token">Token a ser consumido</param>
    /// <param name="ipAddress">Endereço IP onde foi usado</param>
    /// <param name="userAgent">User Agent onde foi usado</param>
    /// <returns>True se consumido com sucesso</returns>
    Task<bool> ConsumeTokenAsync(string token, string? ipAddress = null, string? userAgent = null);

    /// <summary>
    /// Invalida todos os tokens de um usuário
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <returns>Número de tokens invalidados</returns>
    Task<int> InvalidateUserTokensAsync(string userId);

    /// <summary>
    /// Limpa tokens expirados
    /// </summary>
    /// <returns>Número de tokens removidos</returns>
    Task<int> CleanupExpiredTokensAsync();

    /// <summary>
    /// Verifica se um usuário pode solicitar um novo token (rate limiting)
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <returns>True se pode solicitar</returns>
    Task<bool> CanRequestTokenAsync(string userId);

    /// <summary>
    /// Verifica rate limiting por IP
    /// </summary>
    /// <param name="ipAddress">Endereço IP</param>
    /// <returns>True se pode solicitar</returns>
    Task<bool> CanRequestTokenByIpAsync(string ipAddress);

    /// <summary>
    /// Gera um token seguro
    /// </summary>
    /// <returns>Token gerado</returns>
    string GenerateSecureToken();
}
