@page "/opc/servers"
@using BOS.Plant.Modules.OpcDa.Interfaces
@using BOS.Plant.Modules.OpcDa.Models
@inject IOpcDiscoveryService OpcDiscovery
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Gerenciar Servidores OPC - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Gerenciar Servidores OPC</MudText>
        <div class="d-flex gap-2">
            <MudTextField @bind-Value="_hostToDiscover"
                         Label="Host"
                         Placeholder="localhost"
                         Variant="Variant.Outlined"
                         Style="width: 200px;"
                         Adornment="Adornment.Start"
                         AdornmentIcon="@Icons.Material.Filled.Computer" />

            <MudButton Variant="Variant.Filled"
                      Color="Color.Primary"
                      StartIcon="@Icons.Material.Filled.Refresh"
                      OnClick="RefreshServers"
                      Disabled="_isLoading">
                @if (_isLoading)
                {
                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                    <span class="ml-2">Descobrindo...</span>
                }
                else
                {
                    <span>Descobrir</span>
                }
            </MudButton>

            <MudButton Variant="Variant.Outlined"
                      Color="Color.Secondary"
                      StartIcon="@Icons.Material.Filled.NetworkCheck"
                      OnClick="DiscoverInNetwork"
                      Disabled="_isLoading">
                Rede Local
            </MudButton>

            @if (_currentServer != null)
            {
                <MudButton Variant="Variant.Filled"
                          Color="Color.Primary"
                          StartIcon="@Icons.Material.Filled.List"
                          Href="/opc/tags"
                          Class="mr-2">
                    Listar Tags
                </MudButton>
                <MudButton Variant="Variant.Outlined"
                          Color="Color.Error"
                          StartIcon="@Icons.Material.Filled.PowerOff"
                          OnClick="DisconnectServer">
                    Desconectar
                </MudButton>
            }
        </div>
    </div>

    <!-- Servidor Atual -->
    @if (_currentServer != null)
    {
        <MudCard Elevation="4" Class="mb-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <div class="d-flex align-center gap-2">
                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                        <MudText Typo="Typo.h6">Servidor Conectado</MudText>
                    </div>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudText Typo="Typo.subtitle1" Style="font-weight: bold;">@_currentServer.DisplayName</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">@_currentServer.ProgId</MudText>
                        <MudText Typo="Typo.caption">@_currentServer.Description</MudText>
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <div class="d-flex flex-column gap-1">
                            <MudText Typo="Typo.caption"><strong>Fornecedor:</strong> @_currentServer.Vendor</MudText>
                            <MudText Typo="Typo.caption"><strong>Versão:</strong> @_currentServer.Version</MudText>
                            <MudText Typo="Typo.caption"><strong>Host:</strong> @_currentServer.Host</MudText>
                        </div>
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }

    <!-- Lista de Servidores Disponíveis -->
    <MudCard Elevation="2">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">Servidores OPC Disponíveis</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            @if (_servers.Any())
            {
                <MudTable Items="_servers" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm"
                         Dense="true">
                    <HeaderContent>
                        <MudTh>Status</MudTh>
                        <MudTh>Nome do Servidor</MudTh>
                        <MudTh>Host</MudTh>
                        <MudTh>ProgID</MudTh>
                        <MudTh>Fornecedor</MudTh>
                        <MudTh>Versão</MudTh>
                        <MudTh>Última Verificação</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <div class="d-flex align-center gap-2">
                                @if (context.IsConnected)
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Small" />
                                    <MudText Typo="Typo.caption" Color="Color.Success">Conectado</MudText>
                                }
                                else if (context.IsAvailable)
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.Circle" Color="Color.Primary" Size="Size.Small" />
                                    <MudText Typo="Typo.caption" Color="Color.Primary">Disponível</MudText>
                                }
                                else
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.Cancel" Color="Color.Error" Size="Size.Small" />
                                    <MudText Typo="Typo.caption" Color="Color.Error">Indisponível</MudText>
                                }
                            </div>
                        </MudTd>
                        <MudTd>
                            <div>
                                <MudText Typo="Typo.body2" Style="font-weight: bold;">@context.DisplayName</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">@context.Description</MudText>
                            </div>
                        </MudTd>
                        <MudTd>
                            <MudChip T="string" Size="Size.Small"
                                   Color="@(context.Host == "localhost" ? Color.Primary : Color.Secondary)">
                                @context.Host
                            </MudChip>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2" Style="font-family: monospace;">@context.ProgId</MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2">@context.Vendor</MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.body2">@context.Version</MudText>
                        </MudTd>
                        <MudTd>
                            <MudText Typo="Typo.caption">@context.LastChecked.ToString("HH:mm:ss")</MudText>
                        </MudTd>
                        <MudTd>
                            @if (context.IsConnected)
                            {
                                <MudButton Variant="Variant.Text" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          StartIcon="@Icons.Material.Filled.List"
                                          Href="/opc/tags">
                                    Ver Tags
                                </MudButton>
                            }
                            else if (context.IsAvailable)
                            {
                                <MudButton Variant="Variant.Filled" 
                                          Color="Color.Primary" 
                                          Size="Size.Small"
                                          StartIcon="@Icons.Material.Filled.Link"
                                          OnClick="@(() => ConnectToServer(context))"
                                          Disabled="_isConnecting">
                                    @if (_isConnecting && _connectingServer == context.ProgId)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    }
                                    else
                                    {
                                        <span>Conectar</span>
                                    }
                                </MudButton>
                            }
                            else
                            {
                                <MudButton Variant="Variant.Text" 
                                          Color="Color.Secondary" 
                                          Size="Size.Small"
                                          StartIcon="@Icons.Material.Filled.Refresh"
                                          OnClick="@(() => CheckServerAvailability(context))">
                                    Verificar
                                </MudButton>
                            }
                        </MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <div class="d-flex justify-center align-center pa-8">
                            <div class="text-center">
                                <MudIcon Icon="@Icons.Material.Filled.Search" Size="Size.Large" Color="Color.Secondary" />
                                <MudText Typo="Typo.body1" Class="mt-2">Nenhum servidor encontrado</MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    Clique em "Descobrir Servidores" para buscar servidores OPC disponíveis
                                </MudText>
                            </div>
                        </div>
                    </NoRecordsContent>
                </MudTable>
            }
            else
            {
                <div class="d-flex justify-center align-center pa-8">
                    <div class="text-center">
                        @if (_isLoading)
                        {
                            <MudProgressCircular Indeterminate="true" />
                            <MudText Typo="Typo.body1" Class="mt-2">Descobrindo servidores OPC...</MudText>
                        }
                        else
                        {
                            <MudIcon Icon="@Icons.Material.Filled.Storage" Size="Size.Large" Color="Color.Secondary" />
                            <MudText Typo="Typo.body1" Class="mt-2">Clique em "Descobrir Servidores" para começar</MudText>
                        }
                    </div>
                </div>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<OpcServerInfo> _servers = new();
    private OpcServerInfo? _currentServer;
    private bool _isLoading = false;
    private bool _isConnecting = false;
    private string? _connectingServer;
    private string _hostToDiscover = "localhost";

    protected override async Task OnInitializedAsync()
    {
        // Verificar se já há um servidor conectado
        _currentServer = OpcDiscovery.GetCurrentServerInfo();
        
        // Descobrir servidores automaticamente na inicialização
        await RefreshServers();
    }

    private async Task RefreshServers()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var host = string.IsNullOrWhiteSpace(_hostToDiscover) ? "localhost" : _hostToDiscover.Trim();
            var servers = await OpcDiscovery.RefreshServerDiscoveryAsync(host);
            _servers = servers.ToList();

            // Atualizar status do servidor atual
            if (_currentServer != null)
            {
                var currentInList = _servers.FirstOrDefault(s => s.ProgId == _currentServer.ProgId);
                if (currentInList != null)
                {
                    currentInList.IsConnected = true;
                }
            }

            var availableCount = _servers.Count(s => s.IsAvailable);
            Snackbar.Add($"Encontrados {_servers.Count} servidores OPC no host '{host}' ({availableCount} disponíveis)",
                availableCount > 0 ? Severity.Success : Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao descobrir servidores: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task DiscoverInNetwork()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            // Lista de hosts comuns na rede local para descoberta
            var networkHosts = new List<string> { "localhost" };

            // Adicionar IPs da rede local (192.168.x.x)
            for (int i = 1; i <= 10; i++)
            {
                networkHosts.Add($"192.168.1.{i}");
                networkHosts.Add($"192.168.0.{i}");
            }

            var servers = await OpcDiscovery.DiscoverServersInNetworkAsync(networkHosts);
            _servers = servers.ToList();

            // Atualizar status do servidor atual
            if (_currentServer != null)
            {
                var currentInList = _servers.FirstOrDefault(s => s.ProgId == _currentServer.ProgId);
                if (currentInList != null)
                {
                    currentInList.IsConnected = true;
                }
            }

            var availableCount = _servers.Count(s => s.IsAvailable);
            var hostsWithServers = _servers.GroupBy(s => s.Host).Count();

            Snackbar.Add($"Descoberta de rede concluída: {_servers.Count} servidores encontrados em {hostsWithServers} hosts ({availableCount} disponíveis)",
                availableCount > 0 ? Severity.Success : Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro na descoberta de rede: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ConnectToServer(OpcServerInfo server)
    {
        try
        {
            _isConnecting = true;
            _connectingServer = server.ProgId;
            StateHasChanged();

            var success = await OpcDiscovery.ConnectToServerAsync(server);
            if (success)
            {
                _currentServer = server;
                server.IsConnected = true;
                
                // Marcar outros servidores como desconectados
                foreach (var s in _servers.Where(s => s.ProgId != server.ProgId))
                {
                    s.IsConnected = false;
                }

                Snackbar.Add($"Conectado com sucesso ao servidor: {server.DisplayName}", Severity.Success);
            }
            else
            {
                Snackbar.Add($"Falha ao conectar ao servidor: {server.DisplayName}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao conectar: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isConnecting = false;
            _connectingServer = null;
            StateHasChanged();
        }
    }

    private async Task DisconnectServer()
    {
        try
        {
            await OpcDiscovery.DisconnectFromServerAsync();
            
            if (_currentServer != null)
            {
                var serverInList = _servers.FirstOrDefault(s => s.ProgId == _currentServer.ProgId);
                if (serverInList != null)
                {
                    serverInList.IsConnected = false;
                }
                
                Snackbar.Add($"Desconectado do servidor: {_currentServer.DisplayName}", Severity.Info);
                _currentServer = null;
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao desconectar: {ex.Message}", Severity.Error);
        }
        
        StateHasChanged();
    }

    private async Task CheckServerAvailability(OpcServerInfo server)
    {
        try
        {
            var isAvailable = await OpcDiscovery.IsServerAvailableAsync(server.ProgId, server.Host);
            server.IsAvailable = isAvailable;
            server.LastChecked = DateTime.UtcNow;
            
            var message = isAvailable ? "Servidor disponível" : "Servidor indisponível";
            var severity = isAvailable ? Severity.Success : Severity.Warning;
            Snackbar.Add($"{server.DisplayName}: {message}", severity);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao verificar servidor: {ex.Message}", Severity.Error);
        }
        
        StateHasChanged();
    }
}
