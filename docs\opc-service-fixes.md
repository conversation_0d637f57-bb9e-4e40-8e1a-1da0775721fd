# Correções do Serviço OPC DA

Este documento descreve as correções implementadas para resolver o erro de crash do serviço OPC DA.

## 🐛 **Problema Original**

O serviço `BOS.Plant.OpcDa.Service.exe` estava crashando com erro não tratado na interface COM:

```
Stack:
   at BOS.Plant.Modules.OpcDa.Com.IOPCGroup.AddItems(Int32, IntPtr, IntPtr ByRef, IntPtr ByRef)
   at BOS.Plant.Modules.OpcDa.Services.OpcDaService.AddItemsToGroupAsync(...)
```

### **Causa Raiz:**
- Tentativa de usar interfaces COM OPC DA sem servidor OPC real instalado
- Falta de tratamento de erro adequado nas chamadas COM
- Ausência de validações de segurança antes das operações COM

## ✅ **Soluções Implementadas**

### **1. Modo Simulação**
Implementado um modo de simulação que funciona sem servidor OPC real:

```csharp
// Simulação de diferentes tipos de dados baseados no nome do tag
if (item.TagName.Contains("Random"))
{
    if (item.TagName.Contains("Boolean"))
        item.Value = _random.Next(0, 2) == 1;
    else if (item.TagName.Contains("Real"))
        item.Value = _random.NextDouble() * 100;
    // ...
}
```

### **2. Tratamento Robusto de Erros**
Adicionado tratamento de erro em múltiplas camadas:

```csharp
// Tratamento individual por grupo
foreach (var group in config.Groups)
{
    try
    {
        var success = await AddGroupAsync(group, cancellationToken);
        if (!success)
        {
            _logger.LogWarning("Falha ao adicionar grupo: {GroupName}", group.Name);
        }
    }
    catch (Exception groupEx)
    {
        _logger.LogError(groupEx, "Erro ao adicionar grupo: {GroupName}", group.Name);
        // Continuar com outros grupos mesmo se um falhar
    }
}
```

### **3. Validações de Segurança**
Implementadas validações antes das operações críticas:

```csharp
// Verificar se há grupos para adicionar
if (config.Groups == null || !config.Groups.Any())
{
    _logger.LogWarning("Nenhum grupo configurado para o servidor OPC");
    SetConnectionStatus(OpcConnectionStatus.Connected);
    return true;
}

// Verificar se há itens para adicionar
if (items == null || items.Count == 0)
{
    _logger.LogWarning("Nenhum item para adicionar ao grupo {GroupName}", groupName);
    return Task.CompletedTask;
}
```

### **4. Simulador de Dados em Tempo Real**
Criado timer que simula diferentes tipos de sinais OPC:

- **Random**: Valores aleatórios
- **Triangle Waves**: Ondas triangulares
- **Saw-toothed Waves**: Ondas dente de serra
- **Square Waves**: Ondas quadradas
- **Bucket Brigade**: Sequências ordenadas

```csharp
private void SimulateDataUpdates(object? state)
{
    foreach (var item in _items.Values.ToList())
    {
        // Simular diferentes padrões baseados no nome do tag
        if (item.TagName.Contains("Triangle"))
        {
            var time = DateTime.Now.Millisecond / 1000.0;
            item.Value = Math.Abs((time % 2) - 1) * 100;
        }
        // ...
        
        // Disparar evento de atualização
        if (!Equals(oldValue, item.Value))
        {
            _totalUpdates++;
            DataUpdated?.Invoke(this, new OpcDataUpdateEventArgs(item));
        }
    }
}
```

### **5. Logs Detalhados**
Adicionados logs específicos para debug:

```csharp
_logger.LogDebug("Iniciando adição de {ItemCount} itens ao grupo {GroupName}", items.Count, groupName);
_logger.LogInformation("Simulação: {Count} itens adicionados ao grupo {GroupName}", items.Count, groupName);
_logger.LogDebug("Item {TagName} adicionado com valor inicial {Value}", item.TagName, item.Value);
```

## 🎯 **Benefícios das Correções**

### **1. Estabilidade**
- ✅ Serviço não crasha mais
- ✅ Tratamento gracioso de erros
- ✅ Continuidade de operação mesmo com falhas parciais

### **2. Funcionalidade**
- ✅ Funciona sem servidor OPC real (modo simulação)
- ✅ Dados realísticos para desenvolvimento e testes
- ✅ Eventos SignalR funcionando corretamente

### **3. Observabilidade**
- ✅ Logs detalhados para debug
- ✅ Estatísticas de performance
- ✅ Monitoramento de erros e reconexões

### **4. Desenvolvimento**
- ✅ Ambiente de desenvolvimento funcional
- ✅ Testes sem dependência de hardware OPC
- ✅ Demonstrações e apresentações

## 🚀 **Como Testar**

### **1. Executar o Serviço OPC DA:**
```bash
cd src/Services/OpcDa
dotnet run
```

**Logs esperados:**
```
11:10:30.123 [INF] Iniciando serviço OPC DA Worker
11:10:30.456 [INF] Conectando ao servidor OPC: Matrikon.OPC.Simulation.1
11:10:30.789 [INF] Simulação: 4 itens adicionados ao grupo ProcessData
11:10:31.012 [INF] Iniciando monitoramento de dados OPC (modo simulação)
```

### **2. Executar a Aplicação Blazor:**
```bash
cd src/BOS.Plant.Dashboard
dotnet run
```

### **3. Verificar Funcionalidade:**
- Acessar `/opc/dashboard`
- Verificar se dados estão sendo atualizados em tempo real
- Confirmar que não há erros nos logs

## 📋 **Configuração de Desenvolvimento**

Para desenvolvimento, use o arquivo `appsettings.Development.json` que tem:
- Logs mais detalhados (Debug level)
- Intervalo de reconexão menor (5 segundos)
- Mais tentativas de reconexão (10)
- Configuração simplificada com menos itens

## 🔄 **Migração para OPC Real**

Quando um servidor OPC real estiver disponível:

1. **Instalar servidor OPC** (ex: Matrikon OPC Server)
2. **Configurar ProgId** correto no appsettings.json
3. **Substituir simulação** por chamadas COM reais
4. **Testar conectividade** com servidor real

## 🛠️ **Próximas Melhorias**

1. **OPC UA Support**: Migrar para OPC UA (mais moderno)
2. **Configuração Dinâmica**: Interface para adicionar/remover tags
3. **Histórico de Dados**: Armazenar dados históricos
4. **Alertas Avançados**: Sistema de alertas baseado em regras
5. **Performance**: Otimizações para grandes volumes de dados

## 📞 **Troubleshooting**

### **Serviço não inicia:**
- Verificar logs em `src/Services/OpcDa/logs/`
- Confirmar que as portas não estão em uso
- Verificar permissões de arquivo

### **Dados não atualizam:**
- Verificar conexão SignalR nos logs
- Confirmar que o timer de simulação está ativo
- Verificar configuração de grupos e itens

### **Erros de COM:**
- Modo simulação deve funcionar sem servidor OPC
- Para servidor real, verificar instalação e registro
- Executar como administrador se necessário
