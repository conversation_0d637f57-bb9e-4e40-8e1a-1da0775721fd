namespace BOS.Plant.Modules.OpcDa.Models;

/// <summary>
/// Configuração para criação de grupo OPC
/// </summary>
public class OpcGroupConfig
{
    /// <summary>
    /// Nome do grupo
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do grupo
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Categoria do grupo (ex: "Produção", "Qualidade", "Manutenção")
    /// </summary>
    public string Category { get; set; } = "Geral";

    /// <summary>
    /// Taxa de atualização em milissegundos
    /// </summary>
    public int UpdateRateMs { get; set; } = 1000;

    /// <summary>
    /// Indica se o grupo está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Prioridade do grupo (0-255, onde 0 é a maior prioridade)
    /// </summary>
    public byte Priority { get; set; } = 128;

    /// <summary>
    /// Deadband percentual para mudanças de valor
    /// </summary>
    public float DeadbandPercent { get; set; } = 0.0f;

    /// <summary>
    /// Tags iniciais para adicionar ao grupo
    /// </summary>
    public List<string> InitialTags { get; set; } = new();

    /// <summary>
    /// Configurações específicas do grupo
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Informações de um grupo OPC
/// </summary>
public class OpcGroupInfo
{
    /// <summary>
    /// Nome do grupo
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do grupo
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Categoria do grupo
    /// </summary>
    public string Category { get; set; } = "Geral";

    /// <summary>
    /// Indica se o grupo está ativo
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Taxa de atualização em milissegundos
    /// </summary>
    public int UpdateRateMs { get; set; }

    /// <summary>
    /// Número de tags no grupo
    /// </summary>
    public int TagCount { get; set; }

    /// <summary>
    /// Data de criação do grupo
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Última atualização do grupo
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Status do grupo
    /// </summary>
    public OpcGroupStatus Status { get; set; } = OpcGroupStatus.Active;

    /// <summary>
    /// Cor para exibição na interface
    /// </summary>
    public string Color => Category.ToLower() switch
    {
        "produção" or "production" => "success",
        "qualidade" or "quality" => "info",
        "manutenção" or "maintenance" => "warning",
        "segurança" or "safety" => "error",
        "energia" or "energy" => "primary",
        _ => "default"
    };

    /// <summary>
    /// Ícone para exibição na interface
    /// </summary>
    public string Icon => Category.ToLower() switch
    {
        "produção" or "production" => "precision_manufacturing",
        "qualidade" or "quality" => "verified",
        "manutenção" or "maintenance" => "build",
        "segurança" or "safety" => "security",
        "energia" or "energy" => "electrical_services",
        _ => "folder"
    };
}

/// <summary>
/// Status de um grupo OPC
/// </summary>
public enum OpcGroupStatus
{
    Active,
    Inactive,
    Error,
    Connecting,
    Disconnected
}

/// <summary>
/// Subscrição de tag em grupo OPC
/// </summary>
public class OpcTagSubscription
{
    /// <summary>
    /// Caminho do tag
    /// </summary>
    public string TagPath { get; set; } = string.Empty;

    /// <summary>
    /// Nome do grupo
    /// </summary>
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// Indica se a subscrição está ativa
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Handle do cliente
    /// </summary>
    public object? ClientHandle { get; set; }

    /// <summary>
    /// Handle do servidor
    /// </summary>
    public object? ServerHandle { get; set; }

    /// <summary>
    /// Último valor lido
    /// </summary>
    public object? LastValue { get; set; }

    /// <summary>
    /// Qualidade do último valor
    /// </summary>
    public string? LastQuality { get; set; }

    /// <summary>
    /// Timestamp da última leitura
    /// </summary>
    public DateTime? LastReadTime { get; set; }

    /// <summary>
    /// Número de atualizações recebidas
    /// </summary>
    public long UpdateCount { get; set; }

    /// <summary>
    /// Data de criação da subscrição
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// Estatísticas de um grupo OPC
/// </summary>
public class OpcGroupStatistics
{
    /// <summary>
    /// Nome do grupo
    /// </summary>
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// Total de tags no grupo
    /// </summary>
    public int TotalTags { get; set; }

    /// <summary>
    /// Tags ativos
    /// </summary>
    public int ActiveTags { get; set; }

    /// <summary>
    /// Tags com boa qualidade
    /// </summary>
    public int GoodQualityTags { get; set; }

    /// <summary>
    /// Tags com qualidade ruim
    /// </summary>
    public int BadQualityTags { get; set; }

    /// <summary>
    /// Total de atualizações recebidas
    /// </summary>
    public long TotalUpdates { get; set; }

    /// <summary>
    /// Atualizações por segundo
    /// </summary>
    public double UpdatesPerSecond { get; set; }

    /// <summary>
    /// Tempo da última atualização
    /// </summary>
    public DateTime? LastUpdateTime { get; set; }

    /// <summary>
    /// Tempo ativo do grupo
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Número de erros
    /// </summary>
    public int ErrorCount { get; set; }

    /// <summary>
    /// Último erro
    /// </summary>
    public string? LastError { get; set; }

    /// <summary>
    /// Uso de memória estimado
    /// </summary>
    public long MemoryUsageBytes { get; set; }
}

/// <summary>
/// Template de grupo OPC para criação rápida
/// </summary>
public class OpcGroupTemplate
{
    /// <summary>
    /// Nome do template
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do template
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Categoria padrão
    /// </summary>
    public string Category { get; set; } = "Geral";

    /// <summary>
    /// Configuração padrão do grupo
    /// </summary>
    public OpcGroupConfig DefaultConfig { get; set; } = new();

    /// <summary>
    /// Padrões de tags para incluir automaticamente
    /// </summary>
    public List<string> TagPatterns { get; set; } = new();

    /// <summary>
    /// Indica se é um template do sistema
    /// </summary>
    public bool IsSystemTemplate { get; set; }
}

/// <summary>
/// Predefined group templates
/// </summary>
public static class OpcGroupTemplates
{
    public static readonly OpcGroupTemplate Production = new()
    {
        Name = "Produção",
        Description = "Grupo para monitoramento de variáveis de produção",
        Category = "Produção",
        DefaultConfig = new OpcGroupConfig
        {
            UpdateRateMs = 500,
            Priority = 64,
            DeadbandPercent = 0.1f
        },
        TagPatterns = new List<string> { "*Production*", "*Prod*", "*Output*" },
        IsSystemTemplate = true
    };

    public static readonly OpcGroupTemplate Quality = new()
    {
        Name = "Qualidade",
        Description = "Grupo para monitoramento de variáveis de qualidade",
        Category = "Qualidade",
        DefaultConfig = new OpcGroupConfig
        {
            UpdateRateMs = 1000,
            Priority = 96,
            DeadbandPercent = 0.05f
        },
        TagPatterns = new List<string> { "*Quality*", "*QC*", "*Test*" },
        IsSystemTemplate = true
    };

    public static readonly OpcGroupTemplate Maintenance = new()
    {
        Name = "Manutenção",
        Description = "Grupo para monitoramento de variáveis de manutenção",
        Category = "Manutenção",
        DefaultConfig = new OpcGroupConfig
        {
            UpdateRateMs = 2000,
            Priority = 128,
            DeadbandPercent = 1.0f
        },
        TagPatterns = new List<string> { "*Maintenance*", "*Maint*", "*Service*" },
        IsSystemTemplate = true
    };

    public static List<OpcGroupTemplate> GetAllTemplates()
    {
        return new List<OpcGroupTemplate> { Production, Quality, Maintenance };
    }
}
