namespace BOS.Plant.Infrastructure.Services.Email.Models;

public class EmailResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
    public string? EmailId { get; set; }
    public DateTime SentAt { get; set; } = DateTime.UtcNow;

    public static EmailResult CreateSuccess(string message = "E-mail enviado com sucesso", string? emailId = null)
    {
        return new EmailResult { Success = true, Message = message, EmailId = emailId };
    }

    public static EmailResult CreateFailure(string message, Exception? exception = null)
    {
        return new EmailResult { Success = false, Message = message, Exception = exception };
    }
}
