using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Implementação do serviço de políticas de senha
/// </summary>
public class PasswordPolicyService : IPasswordPolicyService
{
    // Configurações de política
    private const int MinLength = 8;
    private const int MaxLength = 128;
    private const int MinStrengthRequired = 60;

    // Lista de senhas comuns (em produção, usar uma lista mais completa)
    private readonly HashSet<string> _commonPasswords = new(StringComparer.OrdinalIgnoreCase)
    {
        "password", "123456", "123456789", "12345678", "12345", "1234567", "admin", "password123",
        "qwerty", "abc123", "Password1", "welcome", "monkey", "dragon", "master", "shadow",
        "letmein", "football", "iloveyou", "sunshine", "princess", "charlie", "rockyou"
    };

    public PasswordValidationResult ValidatePassword(string password)
    {
        var result = new PasswordValidationResult();

        if (string.IsNullOrEmpty(password))
        {
            result.Errors.Add("Senha é obrigatória");
            return result;
        }

        // Validar comprimento
        if (password.Length < MinLength)
        {
            result.Errors.Add($"Senha deve ter pelo menos {MinLength} caracteres");
        }

        if (password.Length > MaxLength)
        {
            result.Errors.Add($"Senha deve ter no máximo {MaxLength} caracteres");
        }

        // Validar complexidade
        if (!password.Any(char.IsLower))
        {
            result.Errors.Add("Senha deve conter pelo menos uma letra minúscula");
            result.Suggestions.Add("Adicione letras minúsculas");
        }

        if (!password.Any(char.IsUpper))
        {
            result.Errors.Add("Senha deve conter pelo menos uma letra maiúscula");
            result.Suggestions.Add("Adicione letras maiúsculas");
        }

        if (!password.Any(char.IsDigit))
        {
            result.Errors.Add("Senha deve conter pelo menos um número");
            result.Suggestions.Add("Adicione números");
        }

        if (!password.Any(c => !char.IsLetterOrDigit(c)))
        {
            result.Errors.Add("Senha deve conter pelo menos um caractere especial");
            result.Suggestions.Add("Adicione caracteres especiais (!@#$%^&*)");
        }

        // Verificar senhas comuns
        if (IsCommonPassword(password))
        {
            result.Errors.Add("Esta senha é muito comum e insegura");
            result.Suggestions.Add("Use uma senha mais única e complexa");
        }

        // Verificar padrões sequenciais
        if (HasSequentialPattern(password))
        {
            result.Errors.Add("Senha não deve conter sequências óbvias");
            result.Suggestions.Add("Evite sequências como 123, abc, qwerty");
        }

        // Calcular força
        result.Strength = CalculatePasswordStrength(password);

        if (result.Strength < MinStrengthRequired)
        {
            result.Errors.Add($"Senha muito fraca (força: {result.Strength}%). Mínimo requerido: {MinStrengthRequired}%");
        }

        result.IsValid = result.Errors.Count == 0;

        return result;
    }

    public int CalculatePasswordStrength(string password)
    {
        if (string.IsNullOrEmpty(password)) return 0;

        int strength = 0;

        // Comprimento (até 40 pontos)
        if (password.Length >= 8) strength += 10;
        if (password.Length >= 10) strength += 10;
        if (password.Length >= 12) strength += 10;
        if (password.Length >= 16) strength += 10;

        // Variedade de caracteres (até 40 pontos)
        if (password.Any(char.IsLower)) strength += 10;
        if (password.Any(char.IsUpper)) strength += 10;
        if (password.Any(char.IsDigit)) strength += 10;
        if (password.Any(c => !char.IsLetterOrDigit(c))) strength += 10;

        // Complexidade adicional (até 20 pontos)
        var uniqueChars = password.Distinct().Count();
        if (uniqueChars >= password.Length * 0.7) strength += 10; // Boa variedade de caracteres

        if (!HasSequentialPattern(password)) strength += 5;
        if (!IsCommonPassword(password)) strength += 5;

        return Math.Min(100, strength);
    }

    public bool IsCommonPassword(string password)
    {
        return _commonPasswords.Contains(password);
    }

    public string GenerateSecurePassword(int length = 12, bool includeSymbols = true)
    {
        const string lowercase = "abcdefghijklmnopqrstuvwxyz";
        const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string digits = "0123456789";
        const string symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var chars = lowercase + uppercase + digits;
        if (includeSymbols)
        {
            chars += symbols;
        }

        var password = new StringBuilder();
        using var rng = RandomNumberGenerator.Create();

        // Garantir pelo menos um caractere de cada tipo
        password.Append(GetRandomChar(lowercase, rng));
        password.Append(GetRandomChar(uppercase, rng));
        password.Append(GetRandomChar(digits, rng));
        
        if (includeSymbols)
        {
            password.Append(GetRandomChar(symbols, rng));
        }

        // Preencher o resto aleatoriamente
        for (int i = password.Length; i < length; i++)
        {
            password.Append(GetRandomChar(chars, rng));
        }

        // Embaralhar a senha
        return new string(password.ToString().OrderBy(x => GetRandomNumber(rng)).ToArray());
    }

    private bool HasSequentialPattern(string password)
    {
        // Verificar sequências numéricas
        if (Regex.IsMatch(password, @"(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)"))
            return true;

        // Verificar sequências alfabéticas
        if (Regex.IsMatch(password, @"(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)", RegexOptions.IgnoreCase))
            return true;

        // Verificar teclado QWERTY
        if (Regex.IsMatch(password, @"(qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)", RegexOptions.IgnoreCase))
            return true;

        return false;
    }

    private char GetRandomChar(string chars, RandomNumberGenerator rng)
    {
        var randomBytes = new byte[4];
        rng.GetBytes(randomBytes);
        var randomIndex = Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % chars.Length;
        return chars[randomIndex];
    }

    private int GetRandomNumber(RandomNumberGenerator rng)
    {
        var randomBytes = new byte[4];
        rng.GetBytes(randomBytes);
        return BitConverter.ToInt32(randomBytes, 0);
    }
}
