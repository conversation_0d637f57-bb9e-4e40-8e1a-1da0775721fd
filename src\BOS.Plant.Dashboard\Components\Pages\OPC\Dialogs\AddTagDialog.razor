@using BOS.Plant.Modules.OpcDa.Models
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 600px;">
            <MudText Typo="Typo.h6" Class="mb-4">Adicionar Tag ao Monitoramento</MudText>
            
            <!-- Informações do Tag -->
            <MudCard Elevation="1" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.subtitle1">Informações do Tag</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12">
                            <MudTextField Value="@Tag?.TagName" 
                                        Label="Nome do Tag" 
                                        ReadOnly="true"
                                        Variant="Variant.Outlined" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudTextField Value="@Tag?.DataType" 
                                        Label="Tipo de Dados" 
                                        ReadOnly="true"
                                        Variant="Variant.Outlined" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudTextField Value="@Tag?.AccessRights.ToString()" 
                                        Label="Direitos de Acesso" 
                                        ReadOnly="true"
                                        Variant="Variant.Outlined" />
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>

            <!-- Configurações de Monitoramento -->
            <MudCard Elevation="1">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.subtitle1">Configurações de Monitoramento</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" md="6">
                            <MudSelect @bind-Value="_selectedGroup" 
                                     Label="Grupo" 
                                     Variant="Variant.Outlined"
                                     Required="true"
                                     RequiredError="Selecione um grupo">
                                @foreach (var group in AvailableGroups)
                                {
                                    <MudSelectItem Value="@group">@group</MudSelectItem>
                                }
                            </MudSelect>
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudNumericField @bind-Value="_updateRate" 
                                           Label="Taxa de Atualização (ms)" 
                                           Variant="Variant.Outlined"
                                           Min="100"
                                           Max="60000"
                                           Step="100" />
                        </MudItem>
                        <MudItem xs="12">
                            <MudTextField @bind-Value="_description" 
                                        Label="Descrição (Opcional)" 
                                        Variant="Variant.Outlined"
                                        Lines="2" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_unit" 
                                        Label="Unidade de Medida (Opcional)" 
                                        Variant="Variant.Outlined" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <!-- Espaço reservado para futuras configurações -->
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudNumericField @bind-Value="_minValue" 
                                           Label="Valor Mínimo (Opcional)" 
                                           Variant="Variant.Outlined" />
                        </MudItem>
                        <MudItem xs="12" md="6">
                            <MudNumericField @bind-Value="_maxValue" 
                                           Label="Valor Máximo (Opcional)" 
                                           Variant="Variant.Outlined" />
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancelar</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="Submit"
                  Disabled="string.IsNullOrEmpty(_selectedGroup)">
            Adicionar ao Monitoramento
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    IMudDialogInstance MudDialog { get; set; } = null!;

    [Parameter] 
    public OpcTagInfo? Tag { get; set; }

    [Parameter] 
    public IEnumerable<string> AvailableGroups { get; set; } = Enumerable.Empty<string>();

    private string _selectedGroup = string.Empty;
    private int _updateRate = 1000;
    private string _description = string.Empty;
    private string _unit = string.Empty;
    private double? _minValue;
    private double? _maxValue;

    protected override void OnInitialized()
    {
        if (Tag != null)
        {
            _description = Tag.Description ?? Tag.DisplayName ?? Tag.TagName;
            
            // Sugerir grupo baseado no caminho do tag
            if (!string.IsNullOrEmpty(Tag.Path))
            {
                var suggestedGroup = AvailableGroups.FirstOrDefault(g => 
                    Tag.Path.Contains(g, StringComparison.OrdinalIgnoreCase));
                
                if (!string.IsNullOrEmpty(suggestedGroup))
                {
                    _selectedGroup = suggestedGroup;
                }
                else
                {
                    _selectedGroup = AvailableGroups.FirstOrDefault() ?? string.Empty;
                }
            }
            else
            {
                _selectedGroup = AvailableGroups.FirstOrDefault() ?? string.Empty;
            }
        }
    }

    private void Submit()
    {
        if (Tag == null || string.IsNullOrEmpty(_selectedGroup))
        {
            Snackbar.Add("Dados inválidos", Severity.Error);
            return;
        }

        var request = new AddTagRequest
        {
            TagName = Tag.TagName,
            GroupName = _selectedGroup,
            UpdateRate = _updateRate,
            Description = string.IsNullOrWhiteSpace(_description) ? null : _description,
            Unit = string.IsNullOrWhiteSpace(_unit) ? null : _unit,
            MinValue = _minValue,
            MaxValue = _maxValue
        };

        MudDialog.Close(DialogResult.Ok(request));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }
}
