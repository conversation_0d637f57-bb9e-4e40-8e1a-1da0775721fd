@page "/projects"
@inject IProjectService ProjectService
@inject IDialogService DialogService
@inject ISnackbar Snackbar

<PageTitle>Projetos - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Projetos</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Add" Href="/projects/create">
            Novo Projeto
        </MudButton>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <div class="d-flex gap-4 mb-4">
                <MudTextField @bind-Value="_searchTerm" 
                             Label="Buscar projetos" 
                             Variant="Variant.Outlined" 
                             Adornment="Adornment.End" 
                             AdornmentIcon="@Icons.Material.Filled.Search"
                             OnKeyUp="@(async (e) => { if (e.Key == "Enter") await SearchProjects(); })" />
                
                <MudSelect @bind-Value="_selectedStatus"
                          Label="Status"
                          Variant="Variant.Outlined"
                          T="ProjectStatus?"
                          Clearable="true">
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)null)">Todos</MudSelectItem>
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)ProjectStatus.Planning)">Planejamento</MudSelectItem>
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)ProjectStatus.InProgress)">Em Andamento</MudSelectItem>
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)ProjectStatus.OnHold)">Pausado</MudSelectItem>
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)ProjectStatus.Completed)">Concluído</MudSelectItem>
                    <MudSelectItem T="ProjectStatus?" Value="@((ProjectStatus?)ProjectStatus.Cancelled)">Cancelado</MudSelectItem>
                </MudSelect>
                
                <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="SearchProjects">
                    Buscar
                </MudButton>
            </div>

            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else
            {
                <MudTable Items="_projects" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm" 
                         Loading="_loading"
                         LoadingProgressColor="Color.Info">
                    <HeaderContent>
                        <MudTh>Nome</MudTh>
                        <MudTh>Proprietário</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Prioridade</MudTh>
                        <MudTh>Progresso</MudTh>
                        <MudTh>Orçamento</MudTh>
                        <MudTh>Criado em</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Nome">
                            <div>
                                <MudText Typo="Typo.body1">@context.Name</MudText>
                                @if (!string.IsNullOrEmpty(context.Description))
                                {
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">@context.Description</MudText>
                                }
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Proprietário">
                            @(context.Owner?.FullName ?? "N/A")
                        </MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip T="string" Color="GetStatusColor(context.Status)" Size="Size.Small">
                                @GetStatusText(context.Status)
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Prioridade">
                            <MudChip T="string" Color="GetPriorityColor(context.Priority)" Size="Size.Small">
                                @GetPriorityText(context.Priority)
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Progresso">
                            <div class="d-flex align-center gap-2">
                                <MudProgressLinear Color="Color.Primary" Value="@context.Progress" Class="flex-grow-1" />
                                <MudText Typo="Typo.caption">@context.Progress%</MudText>
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Orçamento">
                            @(context.Budget?.ToString("C") ?? "N/A")
                        </MudTd>
                        <MudTd DataLabel="Criado em">
                            @context.CreatedAt.ToString("dd/MM/yyyy")
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             Href="@($"/projects/edit/{context.Id}")" />
                                <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                             Color="Color.Error" 
                                             Size="Size.Small"
                                             OnClick="@(() => DeleteProject(context))" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            Nenhum projeto encontrado
                        </MudText>
                    </NoRecordsContent>
                </MudTable>

                @if (_pagedResult.TotalPages > 1)
                {
                    <div class="d-flex justify-center mt-4">
                        <MudPagination Count="_pagedResult.TotalPages" 
                                     Selected="_currentPage" 
                                     SelectedChanged="OnPageChanged" 
                                     ShowFirstButton="true" 
                                     ShowLastButton="true" />
                    </div>
                }
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private List<Project> _projects = new();
    private PagedResult<Project> _pagedResult = new();
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private ProjectStatus? _selectedStatus;
    private int _currentPage = 1;
    private const int PageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadProjects();
    }

    private async Task LoadProjects()
    {
        _loading = true;
        try
        {
            _pagedResult = await ProjectService.GetProjectsPagedAsync(_currentPage, PageSize, _searchTerm, _selectedStatus);
            _projects = _pagedResult.Items.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar projetos: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SearchProjects()
    {
        _currentPage = 1;
        await LoadProjects();
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await LoadProjects();
    }

    private async Task DeleteProject(Project project)
    {
        var parameters = new DialogParameters
        {
            ["ContentText"] = $"Tem certeza que deseja excluir o projeto '{project.Name}'?",
            ["ButtonText"] = "Excluir",
            ["Color"] = Color.Error
        };

        var dialog = await DialogService.ShowAsync<ConfirmDialog>("Confirmar Exclusão", parameters);
        var result = await dialog.Result;

        if (!result.Canceled)
        {
            try
            {
                var deleteResult = await ProjectService.DeleteProjectAsync(project.Id);
                if (deleteResult.Success)
                {
                    Snackbar.Add("Projeto excluído com sucesso", Severity.Success);
                    await LoadProjects();
                }
                else
                {
                    Snackbar.Add(deleteResult.Message ?? "Erro ao excluir projeto", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao excluir projeto: {ex.Message}", Severity.Error);
            }
        }
    }

    private Color GetStatusColor(ProjectStatus status)
    {
        return status switch
        {
            ProjectStatus.Planning => Color.Info,
            ProjectStatus.InProgress => Color.Primary,
            ProjectStatus.OnHold => Color.Warning,
            ProjectStatus.Completed => Color.Success,
            ProjectStatus.Cancelled => Color.Error,
            _ => Color.Default
        };
    }

    private string GetStatusText(ProjectStatus status)
    {
        return status switch
        {
            ProjectStatus.Planning => "Planejamento",
            ProjectStatus.InProgress => "Em Andamento",
            ProjectStatus.OnHold => "Pausado",
            ProjectStatus.Completed => "Concluído",
            ProjectStatus.Cancelled => "Cancelado",
            _ => "Desconhecido"
        };
    }

    private Color GetPriorityColor(ProjectPriority priority)
    {
        return priority switch
        {
            ProjectPriority.Low => Color.Success,
            ProjectPriority.Medium => Color.Warning,
            ProjectPriority.High => Color.Error,
            ProjectPriority.Critical => Color.Error,
            _ => Color.Default
        };
    }

    private string GetPriorityText(ProjectPriority priority)
    {
        return priority switch
        {
            ProjectPriority.Low => "Baixa",
            ProjectPriority.Medium => "Média",
            ProjectPriority.High => "Alta",
            ProjectPriority.Critical => "Crítica",
            _ => "Desconhecida"
        };
    }
}
