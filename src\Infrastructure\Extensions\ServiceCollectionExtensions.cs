using BOS.Plant.Core.Interfaces;
using BOS.Plant.Infrastructure.Data;
using BOS.Plant.Infrastructure.Repositories;
using BOS.Plant.Infrastructure.Services;
using BOS.Plant.Infrastructure.Services.Email;
using BOS.Plant.Infrastructure.Services.Email.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace BOS.Plant.Infrastructure.Extensions;

/// <summary>
/// Extensões para configuração da infraestrutura
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adiciona os serviços de infraestrutura
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Register DbContext with SQLite
        services.AddDbContext<ApplicationDbContext>((serviceProvider, options) =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            options.UseSqlite(connectionString);
        });

        // Registrar serviços
        services.AddScoped<ITenantService, TenantService>();
        services.AddScoped<IUnitOfWork, UnitOfWork>();

        // Registrar repositórios
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped(typeof(ITenantRepository<>), typeof(TenantRepository<>));
        services.AddScoped<IPasswordResetTokenRepository, PasswordResetTokenRepository>();

        // Configurar e registrar serviços de e-mail
        services.AddSingleton<EmailSettings>(provider =>
        {
            var section = configuration.GetSection(EmailSettings.SectionName);
            return new EmailSettings
            {
                Enabled = bool.Parse(section["Enabled"] ?? "false"),
                SmtpServer = section["SmtpServer"] ?? "",
                SmtpPort = int.Parse(section["SmtpPort"] ?? "587"),
                EnableSsl = bool.Parse(section["EnableSsl"] ?? "true"),
                UserName = section["UserName"] ?? "",
                Password = section["Password"] ?? "",
                FromEmail = section["FromEmail"] ?? "",
                FromName = section["FromName"] ?? "",
                ReplyToEmail = section["ReplyToEmail"],
                TimeoutSeconds = int.Parse(section["TimeoutSeconds"] ?? "30"),
                BaseUrl = section["BaseUrl"] ?? ""
            };
        });
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IEmailTemplateService, EmailTemplateService>();

        // Registrar serviços de tokens
        services.AddScoped<IPasswordResetTokenService, PasswordResetTokenService>();

        // Registrar serviços de auditoria
        services.AddScoped<IAuditService, AuditService>();

        // Registrar serviços de segurança
        services.AddScoped<IPasswordPolicyService, PasswordPolicyService>();

        // Registrar serviços em background
        services.AddHostedService<TokenCleanupService>();
        services.AddHostedService<DatabaseMigrationService>();
        services.AddHostedService<DatabaseSeedService>();

        // Registrar serviços de contexto de tenant
        services.AddScoped<ITenantContextFactory, TenantContextFactory>();

        return services;
    }

    /// <summary>
    /// Adiciona suporte a multi-tenancy
    /// </summary>
    public static IServiceCollection AddMultiTenancy(this IServiceCollection services)
    {
        // O TenantService já é registrado como Scoped no AddInfrastructure
        // Aqui podemos adicionar configurações específicas de multi-tenancy se necessário
        
        return services;
    }
}
