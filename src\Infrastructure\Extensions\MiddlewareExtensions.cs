using BOS.Plant.Infrastructure.Middleware;
using Microsoft.AspNetCore.Builder;

namespace BOS.Plant.Infrastructure.Extensions;

/// <summary>
/// Extensões para configuração de middlewares
/// </summary>
public static class MiddlewareExtensions
{
    /// <summary>
    /// Adiciona o middleware de rate limiting
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseRateLimiting(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RateLimitingMiddleware>();
    }
}
