@page "/debug"
@using BOS.Plant.Core.Interfaces
@using BOS.Plant.Core.Entities
@inject ITenantRepository<ApplicationUser> UserRepository

<PageTitle>Debug - Verificação de Usuário</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-8">
    <MudCard>
        <MudCardContent>
            <MudText Typo="Typo.h4" Class="mb-4">Debug - Informações do Usuário Admin</MudText>
            
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="LoadUserInfo" Class="mb-4">
                Carregar Informações do Admin
            </MudButton>
            
            @if (user != null)
            {
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudCard Elevation="2">
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">Informações do Usuário</MudText>
                                <MudText><strong>ID:</strong> @user.Id</MudText>
                                <MudText><strong>Email:</strong> @user.Email</MudText>
                                <MudText><strong>UserName:</strong> @user.UserName</MudText>
                                <MudText><strong>FullName:</strong> @user.FullName</MudText>
                                <MudText><strong>IsActive:</strong> @user.IsActive</MudText>
                                <MudText><strong>TenantId:</strong> @user.TenantId</MudText>
                                <MudText><strong>PasswordHash:</strong> @user.PasswordHash</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudCard Elevation="2">
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">Teste de Hash</MudText>
                                
                                <MudTextField @bind-Value="testPassword" Label="Senha para testar" Class="mb-2" />
                                <MudButton Variant="Variant.Outlined" Color="Color.Secondary" OnClick="TestPasswordHash" Class="mb-2">
                                    Gerar Hash da Senha
                                </MudButton>
                                
                                @if (!string.IsNullOrEmpty(generatedHash))
                                {
                                    <MudText><strong>Hash Gerado:</strong> @generatedHash</MudText>
                                    <MudText><strong>Hashes Coincidem:</strong> @(generatedHash == user.PasswordHash ? "SIM" : "NÃO")</MudText>
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            }
            
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <MudAlert Severity="Severity.Error" Class="mt-4">
                    @errorMessage
                </MudAlert>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private ApplicationUser? user;
    private string testPassword = "Admin123!";
    private string generatedHash = "";
    private string errorMessage = "";

    private async Task LoadUserInfo()
    {
        try
        {
            errorMessage = "";
            user = await UserRepository.GetFirstOrDefaultAsync(u => 
                u.Email == "<EMAIL>");
                
            if (user == null)
            {
                errorMessage = "Usuário admin não encontrado!";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Erro ao carregar usuário: {ex.Message}";
        }
    }

    private void TestPasswordHash()
    {
        try
        {
            generatedHash = HashPassword(testPassword);
        }
        catch (Exception ex)
        {
            errorMessage = $"Erro ao gerar hash: {ex.Message}";
        }
    }

    private static string HashPassword(string password)
    {
        // Implementação simples de hash - em produção usar BCrypt ou similar
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "SALT"));
        return Convert.ToBase64String(hashedBytes);
    }
}
