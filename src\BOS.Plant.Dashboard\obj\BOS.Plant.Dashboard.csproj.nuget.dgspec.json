{"format": 1, "restore": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj": {}}, "projects": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\BOS.Plant.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\BOS.Plant.Application.csproj", "projectName": "BOS.Plant.Application", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\BOS.Plant.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj", "projectName": "BOS.Plant.Dashboard", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\BOS.Plant.Dashboard.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\BOS.Plant.Dashboard\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\BOS.Plant.Application.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Application\\BOS.Plant.Application.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\BOS.Plant.Modules.Auth.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\BOS.Plant.Modules.Auth.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\BOS.Plant.Modules.OpcDa.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\BOS.Plant.Modules.OpcDa.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\BOS.Plant.Modules.Tenants.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\BOS.Plant.Modules.Tenants.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Extensions.MudBlazor.StaticInput": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Net.Http.Headers": {"target": "Package", "version": "[9.0.6, )"}, "MudBlazor": {"target": "Package", "version": "[8.9.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj", "projectName": "BOS.Plant.Core", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Nanoid": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj", "projectName": "BOS.Plant.Infrastructure", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\BOS.Plant.Modules.Auth.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\BOS.Plant.Modules.Auth.csproj", "projectName": "BOS.Plant.Modules.Auth", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\BOS.Plant.Modules.Auth.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Auth\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\BOS.Plant.Modules.OpcDa.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\BOS.Plant.Modules.OpcDa.csproj", "projectName": "BOS.Plant.Modules.OpcDa", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\BOS.Plant.Modules.OpcDa.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\OpcDa\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.6, )"}, "OpcNetApi": {"target": "Package", "version": "[2.1.108, )"}, "OpcNetApi.Com": {"target": "Package", "version": "[2.1.108, )"}, "System.Runtime.InteropServices": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj", "projectName": "BOS.Plant.Modules.Projects", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\BOS.Plant.Modules.Projects.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Projects\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\BOS.Plant.Modules.Tenants.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\BOS.Plant.Modules.Tenants.csproj", "projectName": "BOS.Plant.Modules.Tenants", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\BOS.Plant.Modules.Tenants.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Tenants\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj", "projectName": "BOS.Plant.Modules.Users", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj", "projectName": "BOS.Plant.Shared", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}