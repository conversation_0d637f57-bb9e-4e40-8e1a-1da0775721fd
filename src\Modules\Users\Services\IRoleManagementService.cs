using BOS.Plant.Core.Entities;
using BOS.Plant.Shared.Models;

namespace BOS.Plant.Modules.Users.Services;

/// <summary>
/// Interface para serviços de gestão de roles
/// </summary>
public interface IRoleManagementService
{
    /// <summary>
    /// Obtém todas as roles do tenant atual
    /// </summary>
    Task<IEnumerable<ApplicationRole>> GetRolesAsync();

    /// <summary>
    /// Obtém roles paginadas
    /// </summary>
    Task<PagedResult<ApplicationRole>> GetRolesPagedAsync(int page, int pageSize, string? searchTerm = null, bool? isActive = null);

    /// <summary>
    /// Obtém role por ID
    /// </summary>
    Task<ApplicationRole?> GetRoleByIdAsync(string roleId);

    /// <summary>
    /// Cria uma nova role
    /// </summary>
    Task<RoleResult> CreateRoleAsync(CreateRoleRequest request);

    /// <summary>
    /// Atualiza uma role
    /// </summary>
    Task<RoleResult> UpdateRoleAsync(string roleId, UpdateRoleRequest request);

    /// <summary>
    /// Ativa/desativa uma role
    /// </summary>
    Task<RoleResult> ToggleRoleStatusAsync(string roleId);

    /// <summary>
    /// Exclui uma role (soft delete)
    /// </summary>
    Task<RoleResult> DeleteRoleAsync(string roleId);

    /// <summary>
    /// Verifica se uma role pode ser excluída
    /// </summary>
    Task<bool> CanDeleteRoleAsync(string roleId);

    /// <summary>
    /// Obtém usuários que possuem uma role específica
    /// </summary>
    Task<IEnumerable<ApplicationUser>> GetUsersInRoleAsync(string roleId);
}

/// <summary>
/// Resultado de operações de role
/// </summary>
public class RoleResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public ApplicationRole? Role { get; set; }
}

/// <summary>
/// Request para criação de role
/// </summary>
public class CreateRoleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request para atualização de role
/// </summary>
public class UpdateRoleRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
}
