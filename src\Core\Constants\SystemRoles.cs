namespace BOS.Plant.Core.Constants;

/// <summary>
/// Roles padrão do sistema
/// </summary>
public static class SystemRoles
{
    /// <summary>
    /// Administrador do sistema (super admin)
    /// </summary>
    public const string SystemAdmin = "SystemAdmin";

    /// <summary>
    /// Administrador do tenant
    /// </summary>
    public const string TenantAdmin = "TenantAdmin";

    /// <summary>
    /// Gerente de projetos
    /// </summary>
    public const string ProjectManager = "ProjectManager";

    /// <summary>
    /// Usuário padrão
    /// </summary>
    public const string User = "User";

    /// <summary>
    /// Usuário somente leitura
    /// </summary>
    public const string ReadOnlyUser = "ReadOnlyUser";

    /// <summary>
    /// Lista de todos os roles do sistema
    /// </summary>
    public static readonly string[] AllRoles = 
    {
        SystemAdmin,
        TenantAdmin,
        ProjectManager,
        User,
        ReadOnlyUser
    };

    /// <summary>
    /// Roles que podem gerenciar usuários
    /// </summary>
    public static readonly string[] UserManagementRoles = 
    {
        SystemAdmin,
        TenantAdmin
    };

    /// <summary>
    /// Roles que podem gerenciar projetos
    /// </summary>
    public static readonly string[] ProjectManagementRoles = 
    {
        SystemAdmin,
        TenantAdmin,
        ProjectManager
    };
}
