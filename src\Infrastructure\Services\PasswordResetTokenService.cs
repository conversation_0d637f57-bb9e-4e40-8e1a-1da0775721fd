using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using BOS.Plant.Core.Entities;
using BOS.Plant.Core.Interfaces;
using BOS.Plant.Infrastructure.Repositories;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Serviço para gerenciamento de tokens de reset de senha
/// </summary>
public class PasswordResetTokenService : IPasswordResetTokenService
{
    private readonly IPasswordResetTokenRepository _tokenRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<PasswordResetTokenService> _logger;

    // Configurações de rate limiting
    private const int MaxTokensPerUser = 3; // Máximo de tokens válidos por usuário
    private const int MaxTokensPerIpPerHour = 5; // Máximo de tokens por IP por hora
    private const int TokenExpirationHours = 24; // Expiração do token em horas

    public PasswordResetTokenService(
        IPasswordResetTokenRepository tokenRepository,
        IUnitOfWork unitOfWork,
        ILogger<PasswordResetTokenService> logger)
    {
        _tokenRepository = tokenRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<PasswordResetToken> GenerateTokenAsync(string userId, string? ipAddress = null, string? userAgent = null)
    {
        try
        {
            // Verificar rate limiting
            if (!await CanRequestTokenAsync(userId))
            {
                throw new InvalidOperationException("Usuário excedeu o limite de tokens de reset");
            }

            if (!string.IsNullOrEmpty(ipAddress) && !await CanRequestTokenByIpAsync(ipAddress))
            {
                throw new InvalidOperationException("IP excedeu o limite de solicitações por hora");
            }

            // Gerar token seguro
            var tokenValue = GenerateSecureToken();
            var hashedToken = HashToken(tokenValue);

            // Criar entidade do token
            var token = PasswordResetToken.Create(
                userId, 
                hashedToken, 
                TokenExpirationHours, 
                ipAddress, 
                userAgent);

            // Salvar no banco
            await _tokenRepository.AddAsync(token);
            await _unitOfWork.SaveChangesAsync();

            // Retornar token com valor original para envio por e-mail
            token.Token = tokenValue;

            _logger.LogInformation("Token de reset gerado para usuário {UserId} do IP {IpAddress}", userId, ipAddress);

            return token;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao gerar token de reset para usuário {UserId}", userId);
            throw;
        }
    }

    public async Task<PasswordResetToken?> ValidateTokenAsync(string token)
    {
        try
        {
            var hashedToken = HashToken(token);
            var tokenEntity = await _tokenRepository.GetValidTokenAsync(hashedToken);

            if (tokenEntity != null)
            {
                _logger.LogInformation("Token válido encontrado para usuário {UserId}", tokenEntity.UserId);
            }
            else
            {
                _logger.LogWarning("Token inválido ou expirado: {Token}", token.Substring(0, Math.Min(10, token.Length)) + "...");
            }

            return tokenEntity;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao validar token de reset");
            return null;
        }
    }

    public async Task<bool> ConsumeTokenAsync(string token, string? ipAddress = null, string? userAgent = null)
    {
        try
        {
            var hashedToken = HashToken(token);
            var tokenEntity = await _tokenRepository.GetValidTokenAsync(hashedToken);

            if (tokenEntity == null)
            {
                return false;
            }

            tokenEntity.MarkAsUsed(ipAddress, userAgent);
            await _tokenRepository.UpdateAsync(tokenEntity);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Token consumido para usuário {UserId} do IP {IpAddress}", tokenEntity.UserId, ipAddress);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao consumir token de reset");
            return false;
        }
    }

    public async Task<int> InvalidateUserTokensAsync(string userId)
    {
        try
        {
            var count = await _tokenRepository.InvalidateUserTokensAsync(userId);
            _logger.LogInformation("Invalidados {Count} tokens para usuário {UserId}", count, userId);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao invalidar tokens do usuário {UserId}", userId);
            return 0;
        }
    }

    public async Task<int> CleanupExpiredTokensAsync()
    {
        try
        {
            var count = await _tokenRepository.CleanupExpiredTokensAsync();
            _logger.LogInformation("Removidos {Count} tokens expirados", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao limpar tokens expirados");
            return 0;
        }
    }

    public async Task<bool> CanRequestTokenAsync(string userId)
    {
        try
        {
            var validTokensCount = await _tokenRepository.CountValidTokensByUserIdAsync(userId);
            return validTokensCount < MaxTokensPerUser;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar rate limiting para usuário {UserId}", userId);
            return false;
        }
    }

    public async Task<bool> CanRequestTokenByIpAsync(string ipAddress)
    {
        try
        {
            var tokens = await _tokenRepository.GetTokensByIpAddressAsync(ipAddress, 1);
            return tokens.Count() < MaxTokensPerIpPerHour;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar rate limiting para IP {IpAddress}", ipAddress);
            return false;
        }
    }

    public string GenerateSecureToken()
    {
        // Gerar 32 bytes aleatórios e converter para Base64URL
        var randomBytes = new byte[32];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(randomBytes);
        }

        return Convert.ToBase64String(randomBytes)
            .Replace('+', '-')
            .Replace('/', '_')
            .Replace("=", "");
    }

    private string HashToken(string token)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(token));
        return Convert.ToBase64String(hashedBytes);
    }
}
