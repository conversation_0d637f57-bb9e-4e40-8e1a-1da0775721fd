using OpcCom;

namespace BOS.Plant.Modules.OpcDa.Models;

/// <summary>
/// Opções de configuração para o cache de conexões OPC
/// </summary>
public class OpcConnectionCacheOptions
{
    /// <summary>
    /// Número máximo de conexões em cache
    /// </summary>
    public int MaxCachedConnections { get; set; } = 50;

    /// <summary>
    /// Idade máxima de uma conexão em minutos
    /// </summary>
    public int MaxConnectionAgeMinutes { get; set; } = 60;

    /// <summary>
    /// Tempo máximo de inatividade em minutos
    /// </summary>
    public int MaxIdleTimeMinutes { get; set; } = 30;

    /// <summary>
    /// Intervalo de limpeza automática em minutos
    /// </summary>
    public int CleanupIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// Timeout para operações de conexão em segundos
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Habilita verificação de saúde das conexões
    /// </summary>
    public bool EnableHealthCheck { get; set; } = true;

    /// <summary>
    /// Intervalo de verificação de saúde em minutos
    /// </summary>
    public int HealthCheckIntervalMinutes { get; set; } = 10;
}

/// <summary>
/// Conexão OPC em cache
/// </summary>
public class OpcCachedConnection
{
    /// <summary>
    /// Chave única da conexão
    /// </summary>
    public string ConnectionKey { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Host do servidor
    /// </summary>
    public string? Host { get; set; }

    /// <summary>
    /// Servidor OPC conectado
    /// </summary>
    public OpcCom.Da.Server Server { get; set; } = null!;

    /// <summary>
    /// Data de criação da conexão
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Último acesso à conexão
    /// </summary>
    public DateTime LastAccessed { get; set; }

    /// <summary>
    /// Número de vezes que a conexão foi acessada
    /// </summary>
    public long AccessCount { get; set; }

    /// <summary>
    /// Indica se a conexão está ativa
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Última verificação de saúde
    /// </summary>
    public DateTime? LastHealthCheck { get; set; }

    /// <summary>
    /// Resultado da última verificação de saúde
    /// </summary>
    public bool? LastHealthCheckResult { get; set; }

    /// <summary>
    /// Metadados adicionais da conexão
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Informações de uma conexão em cache
/// </summary>
public class OpcCachedConnectionInfo
{
    /// <summary>
    /// Chave da conexão
    /// </summary>
    public string ConnectionKey { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Host do servidor
    /// </summary>
    public string? Host { get; set; }

    /// <summary>
    /// Data de criação
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Último acesso
    /// </summary>
    public DateTime LastAccessed { get; set; }

    /// <summary>
    /// Número de acessos
    /// </summary>
    public long AccessCount { get; set; }

    /// <summary>
    /// Indica se a conexão é válida
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// Indica se a conexão expirou
    /// </summary>
    public bool IsExpired { get; set; }

    /// <summary>
    /// Idade da conexão
    /// </summary>
    public TimeSpan Age => DateTime.Now - CreatedAt;

    /// <summary>
    /// Tempo desde último acesso
    /// </summary>
    public TimeSpan IdleTime => DateTime.Now - LastAccessed;

    /// <summary>
    /// Status da conexão
    /// </summary>
    public string Status => IsExpired ? "Expired" : IsValid ? "Active" : "Invalid";
}

/// <summary>
/// Estatísticas do cache de conexões
/// </summary>
public class OpcConnectionCacheStatistics
{
    /// <summary>
    /// Total de conexões no cache
    /// </summary>
    public int TotalConnections { get; set; }

    /// <summary>
    /// Conexões ativas
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// Conexões expiradas
    /// </summary>
    public int ExpiredConnections { get; set; }

    /// <summary>
    /// Total de acessos a conexões
    /// </summary>
    public long TotalAccessCount { get; set; }

    /// <summary>
    /// Idade média das conexões
    /// </summary>
    public TimeSpan AverageConnectionAge { get; set; }

    /// <summary>
    /// Uso estimado de memória em bytes
    /// </summary>
    public long MemoryUsageEstimate { get; set; }

    /// <summary>
    /// Taxa de acerto do cache (%)
    /// </summary>
    public double CacheHitRate { get; set; }

    /// <summary>
    /// Última limpeza do cache
    /// </summary>
    public DateTime? LastCleanup { get; set; }

    /// <summary>
    /// Número de conexões removidas na última limpeza
    /// </summary>
    public int LastCleanupRemovedCount { get; set; }

    /// <summary>
    /// Eficiência do cache
    /// </summary>
    public string Efficiency => CacheHitRate switch
    {
        >= 90 => "Excelente",
        >= 75 => "Boa",
        >= 50 => "Regular",
        >= 25 => "Baixa",
        _ => "Muito Baixa"
    };
}

/// <summary>
/// Argumentos do evento de cache de conexão
/// </summary>
public class OpcConnectionCacheEventArgs : EventArgs
{
    /// <summary>
    /// Chave da conexão
    /// </summary>
    public string ConnectionKey { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Razão do evento
    /// </summary>
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// Dados adicionais do evento
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// Configuração de política de cache
/// </summary>
public class OpcCachePolicyConfig
{
    /// <summary>
    /// Política de expiração
    /// </summary>
    public CacheExpirationPolicy ExpirationPolicy { get; set; } = CacheExpirationPolicy.TimeAndIdle;

    /// <summary>
    /// Política de remoção quando cache está cheio
    /// </summary>
    public CacheEvictionPolicy EvictionPolicy { get; set; } = CacheEvictionPolicy.LeastRecentlyUsed;

    /// <summary>
    /// Prioridade de conexões por servidor
    /// </summary>
    public Dictionary<string, int> ServerPriorities { get; set; } = new();

    /// <summary>
    /// Configurações específicas por servidor
    /// </summary>
    public Dictionary<string, OpcServerCacheConfig> ServerConfigs { get; set; } = new();
}

/// <summary>
/// Política de expiração do cache
/// </summary>
public enum CacheExpirationPolicy
{
    /// <summary>
    /// Apenas por tempo máximo
    /// </summary>
    TimeOnly,
    
    /// <summary>
    /// Apenas por tempo de inatividade
    /// </summary>
    IdleOnly,
    
    /// <summary>
    /// Por tempo máximo OU tempo de inatividade
    /// </summary>
    TimeOrIdle,
    
    /// <summary>
    /// Por tempo máximo E tempo de inatividade
    /// </summary>
    TimeAndIdle
}

/// <summary>
/// Política de remoção do cache
/// </summary>
public enum CacheEvictionPolicy
{
    /// <summary>
    /// Menos recentemente usado
    /// </summary>
    LeastRecentlyUsed,
    
    /// <summary>
    /// Menos frequentemente usado
    /// </summary>
    LeastFrequentlyUsed,
    
    /// <summary>
    /// Primeiro a entrar, primeiro a sair
    /// </summary>
    FirstInFirstOut,
    
    /// <summary>
    /// Baseado em prioridade
    /// </summary>
    Priority
}

/// <summary>
/// Configuração de cache específica por servidor
/// </summary>
public class OpcServerCacheConfig
{
    /// <summary>
    /// Prioridade do servidor (maior = mais importante)
    /// </summary>
    public int Priority { get; set; } = 1;

    /// <summary>
    /// Tempo máximo de vida em minutos
    /// </summary>
    public int? MaxAgeMinutes { get; set; }

    /// <summary>
    /// Tempo máximo de inatividade em minutos
    /// </summary>
    public int? MaxIdleMinutes { get; set; }

    /// <summary>
    /// Indica se deve manter conexão sempre ativa
    /// </summary>
    public bool KeepAlive { get; set; }

    /// <summary>
    /// Configurações específicas
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}
