// Script para testar a aplicação BOS Plant System no navegador
// Copie e cole este código no console do navegador (F12 -> Console)

console.log('🚀 Iniciando teste da aplicação BOS Plant System...');

// Função para aguardar
const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Função principal de teste
async function testApplication() {
    try {
        console.log('📍 URL atual:', window.location.href);
        console.log('📄 Título da página:', document.title);
        
        // Verificar se há erros visíveis na página
        console.log('\n🔍 Verificando erros na página...');
        
        const pageContent = document.body.textContent || '';
        const errorIndicators = [
            'error', 'erro', 'exception', 'null reference', 
            'object reference not set', 'internal server error', 
            '500', 'stack trace', 'unhandled exception',
            'system.nullreferenceexception', 'blazor error'
        ];
        
        let errorsFound = [];
        for (const indicator of errorIndicators) {
            if (pageContent.toLowerCase().includes(indicator.toLowerCase())) {
                errorsFound.push(indicator);
            }
        }
        
        if (errorsFound.length > 0) {
            console.log('❌ Possíveis erros encontrados na página:');
            errorsFound.forEach(error => console.log(`   - ${error}`));
        } else {
            console.log('✅ Nenhum erro óbvio encontrado no conteúdo da página');
        }
        
        // Verificar erros específicos do Blazor
        const blazorErrors = document.querySelectorAll('.blazor-error-ui, #blazor-error-ui');
        if (blazorErrors.length > 0) {
            console.log('❌ Erros do Blazor encontrados!');
            blazorErrors.forEach((error, index) => {
                console.log(`   Erro ${index + 1}:`, error.textContent);
            });
        }
        
        // Verificar se há elementos de login
        console.log('\n🔐 Verificando estado de autenticação...');
        const loginElements = document.querySelectorAll('input[type="email"], input[type="password"]');
        const loginButtons = Array.from(document.querySelectorAll('button, a')).filter(el => {
            const text = el.textContent || '';
            return text.includes('Login') || text.includes('Entrar');
        });
        
        if (loginElements.length > 0 || loginButtons.length > 0) {
            console.log('🔑 Página de login detectada');
            console.log('   - Campos de login:', loginElements.length);
            console.log('   - Botões de login:', loginButtons.length);
            console.log('ℹ️ Faça login e execute o teste novamente');
            return;
        } else {
            console.log('✅ Usuário parece estar logado');
        }
        
        // Procurar pelo menu de projetos
        console.log('\n🔍 Procurando pelo menu de projetos...');
        
        const projectLinks = Array.from(document.querySelectorAll('a')).filter(a => {
            const href = a.getAttribute('href') || '';
            const text = a.textContent || '';
            return href.includes('/projects') || 
                   text.includes('Projetos') || 
                   text.includes('Listar Projetos');
        });
        
        if (projectLinks.length > 0) {
            console.log(`✅ ${projectLinks.length} link(s) de projetos encontrado(s):`);
            projectLinks.forEach((link, index) => {
                console.log(`   ${index + 1}. "${link.textContent.trim()}" -> ${link.getAttribute('href')}`);
            });
            
            // Clicar no primeiro link de projetos
            console.log('\n🔗 Clicando no primeiro link de projetos...');
            projectLinks[0].click();
            
            // Aguardar navegação
            await wait(3000);
            
            console.log('📍 Nova URL:', window.location.href);
            
        } else {
            console.log('❌ Nenhum link de projetos encontrado');
            console.log('🔗 Tentando navegar diretamente para /projects...');
            window.location.href = '/projects';
            await wait(3000);
        }
        
        // Verificar a página de projetos
        console.log('\n📊 Analisando a página de projetos...');
        
        // Verificar elementos da página
        const tables = document.querySelectorAll('table, .mud-table');
        const cards = document.querySelectorAll('.card, .mud-card');
        const loadingIndicators = document.querySelectorAll('.mud-progress-circular, .loading, .spinner');
        const actionButtons = Array.from(document.querySelectorAll('button, a')).filter(el => {
            const text = el.textContent || '';
            return text.includes('Criar') || text.includes('Adicionar') || text.includes('Novo');
        });
        
        console.log(`📋 Elementos encontrados:`);
        console.log(`   - Tabelas: ${tables.length}`);
        console.log(`   - Cards: ${cards.length}`);
        console.log(`   - Indicadores de carregamento: ${loadingIndicators.length}`);
        console.log(`   - Botões de ação: ${actionButtons.length}`);
        
        // Verificar se há dados na tabela
        if (tables.length > 0) {
            const rows = tables[0].querySelectorAll('tbody tr, .mud-table-body tr');
            console.log(`   - Linhas na tabela: ${rows.length}`);
            
            if (rows.length === 0) {
                console.log('ℹ️ Tabela vazia - pode ser normal se não há projetos cadastrados');
            }
        }
        
        // Verificar mensagens de "sem dados"
        const noDataMessages = Array.from(document.querySelectorAll('*')).filter(el => {
            const text = el.textContent || '';
            return text.includes('Nenhum projeto') || 
                   text.includes('No projects') || 
                   text.includes('Nenhum registro');
        });
        
        if (noDataMessages.length > 0) {
            console.log(`ℹ️ Mensagens de "sem dados" encontradas: ${noDataMessages.length}`);
            noDataMessages.forEach((msg, index) => {
                console.log(`   ${index + 1}. "${msg.textContent.trim()}"`);
            });
        }
        
        // Verificar novamente por erros após navegação
        console.log('\n🔍 Verificação final de erros...');
        const finalPageContent = document.body.textContent || '';
        let finalErrorsFound = [];
        
        for (const indicator of errorIndicators) {
            if (finalPageContent.toLowerCase().includes(indicator.toLowerCase())) {
                finalErrorsFound.push(indicator);
            }
        }
        
        if (finalErrorsFound.length > 0) {
            console.log('❌ Erros encontrados após navegação:');
            finalErrorsFound.forEach(error => console.log(`   - ${error}`));
        } else {
            console.log('✅ Nenhum erro encontrado após navegação');
        }
        
        console.log('\n🎉 Teste concluído!');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error);
    }
}

// Função para navegar diretamente para projetos
function goToProjects() {
    console.log('🔗 Navegando diretamente para /projects...');
    window.location.href = '/projects';
}

// Função para verificar logs do console
function checkConsoleLogs() {
    console.log('📝 Monitorando logs do console por 10 segundos...');
    
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;
    
    const errors = [];
    const warnings = [];
    const logs = [];
    
    console.error = function(...args) {
        errors.push(args.join(' '));
        originalError.apply(console, args);
    };
    
    console.warn = function(...args) {
        warnings.push(args.join(' '));
        originalWarn.apply(console, args);
    };
    
    console.log = function(...args) {
        const message = args.join(' ');
        if (!message.includes('📝 Monitorando') && !message.includes('📊 Resumo')) {
            logs.push(message);
        }
        originalLog.apply(console, args);
    };
    
    setTimeout(() => {
        console.log('📊 Resumo dos logs capturados:');
        console.log(`   - Erros: ${errors.length}`);
        console.log(`   - Avisos: ${warnings.length}`);
        console.log(`   - Logs: ${logs.length}`);
        
        if (errors.length > 0) {
            console.log('\n❌ Erros capturados:');
            errors.forEach((error, index) => console.log(`   ${index + 1}. ${error}`));
        }
        
        if (warnings.length > 0) {
            console.log('\n⚠️ Avisos capturados:');
            warnings.forEach((warning, index) => console.log(`   ${index + 1}. ${warning}`));
        }
        
        // Restaurar console original
        console.error = originalError;
        console.warn = originalWarn;
        console.log = originalLog;
        
        console.log('\n✅ Monitoramento de logs concluído');
    }, 10000);
}

// Instruções para o usuário
console.log('\n📋 Comandos disponíveis:');
console.log('   testApplication() - Executa o teste completo');
console.log('   goToProjects() - Navega diretamente para /projects');
console.log('   checkConsoleLogs() - Monitora logs por 10 segundos');
console.log('\n💡 Execute: testApplication()');
