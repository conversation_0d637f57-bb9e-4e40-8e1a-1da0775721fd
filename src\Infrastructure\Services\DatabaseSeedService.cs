using BOS.Plant.Core.Entities;
using BOS.Plant.Core.Enums;
using BOS.Plant.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Serviço para popular o banco de dados com dados iniciais
/// </summary>
public class DatabaseSeedService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseSeedService> _logger;

    public DatabaseSeedService(IServiceProvider serviceProvider, ILogger<DatabaseSeedService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        try
        {
            _logger.LogInformation("Iniciando seed do banco de dados...");

            await SeedTenantAsync(context);
            await SeedRolesAsync(context);
            await SeedAdminUserAsync(context);

            _logger.LogInformation("Seed do banco de dados concluído com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer seed do banco de dados");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    private async Task SeedTenantAsync(ApplicationDbContext context)
    {
        const string defaultTenantId = "default-tenant";
        
        var existingTenant = await context.Tenants
            .FirstOrDefaultAsync(t => t.Id == defaultTenantId);

        if (existingTenant == null)
        {
            var tenant = new Tenant
            {
                Id = defaultTenantId,
                Name = "Default Tenant",
                Subdomain = "default",
                DatabaseStrategy = TenantDatabaseStrategy.Shared,
                IsActive = true,
                MaxUsers = 100,
                MaxProjects = 50,
                SubscriptionExpiresAt = DateTime.UtcNow.AddYears(1),
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Tenants.Add(tenant);
            await context.SaveChangesAsync();
            
            _logger.LogInformation("Tenant padrão criado: {TenantId}", defaultTenantId);
        }
    }

    private async Task SeedRolesAsync(ApplicationDbContext context)
    {
        const string defaultTenantId = "default-tenant";
        
        var roles = new[]
        {
            new { Name = "SuperAdmin", Description = "Administrador do sistema com acesso total" },
            new { Name = "Admin", Description = "Administrador do tenant com acesso completo" },
            new { Name = "Manager", Description = "Gerente com acesso a projetos e usuários" },
            new { Name = "User", Description = "Usuário padrão com acesso básico" }
        };

        foreach (var roleData in roles)
        {
            var existingRole = await context.Roles
                .FirstOrDefaultAsync(r => r.TenantId == defaultTenantId && r.Name == roleData.Name);

            if (existingRole == null)
            {
                var role = new ApplicationRole
                {
                    Id = Guid.NewGuid().ToString("N")[..21],
                    TenantId = defaultTenantId,
                    Name = roleData.Name,
                    NormalizedName = roleData.Name.ToUpperInvariant(),
                    Description = roleData.Description,
                    IsActive = true,
                    IsSystemRole = true, // Roles iniciais são do sistema
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                context.Roles.Add(role);
                _logger.LogInformation("Role criada: {RoleName}", roleData.Name);
            }
        }

        await context.SaveChangesAsync();
    }

    private async Task SeedAdminUserAsync(ApplicationDbContext context)
    {
        const string defaultTenantId = "default-tenant";
        const string adminEmail = "<EMAIL>";
        
        var existingUser = await context.Users
            .FirstOrDefaultAsync(u => u.TenantId == defaultTenantId && u.Email == adminEmail);

        if (existingUser == null)
        {
            var adminUser = new ApplicationUser
            {
                Id = Guid.NewGuid().ToString("N")[..21],
                TenantId = defaultTenantId,
                FullName = "Administrador do Sistema",
                Email = adminEmail,
                UserName = "admin",
                PasswordHash = HashPassword("Admin123!"),
                EmailConfirmed = true,
                PhoneNumberConfirmed = false,
                TwoFactorEnabled = false,
                LockoutEnabled = false,
                AccessFailedCount = 0,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            context.Users.Add(adminUser);
            await context.SaveChangesAsync();

            // Atribuir role de SuperAdmin
            var superAdminRole = await context.Roles
                .FirstOrDefaultAsync(r => r.TenantId == defaultTenantId && r.Name == "SuperAdmin");

            if (superAdminRole != null)
            {
                var userRole = new UserRole
                {
                    UserId = adminUser.Id,
                    RoleId = superAdminRole.Id,
                    CreatedAt = DateTime.UtcNow
                };

                context.UserRoles.Add(userRole);
                await context.SaveChangesAsync();
            }

            _logger.LogInformation("Usuário administrador criado: {Email}", adminEmail);
            _logger.LogInformation("Senha padrão: Admin123!");
        }
    }

    private static string HashPassword(string password)
    {
        // Implementação simples de hash - em produção usar BCrypt ou similar
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT"));
        return Convert.ToBase64String(hashedBytes);
    }
}
