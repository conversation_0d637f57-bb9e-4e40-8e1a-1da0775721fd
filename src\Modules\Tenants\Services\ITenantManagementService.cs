using BOS.Plant.Core.Entities;
using BOS.Plant.Shared.Models;

namespace BOS.Plant.Modules.Tenants.Services;

/// <summary>
/// Interface para serviços de gestão de tenant
/// </summary>
public interface ITenantManagementService
{
    /// <summary>
    /// Obtém o tenant atual
    /// </summary>
    Task<Tenant?> GetCurrentTenantAsync();

    /// <summary>
    /// Atualiza as configurações do tenant atual
    /// </summary>
    Task<TenantResult> UpdateTenantSettingsAsync(UpdateTenantSettingsRequest request);

    /// <summary>
    /// Obtém usuários do tenant atual
    /// </summary>
    Task<PagedResult<ApplicationUser>> GetTenantUsersAsync(int page, int pageSize, string? searchTerm = null);

    /// <summary>
    /// Obtém estatísticas do tenant
    /// </summary>
    Task<TenantStatistics> GetTenantStatisticsAsync();

    /// <summary>
    /// Verifica limites do tenant
    /// </summary>
    Task<TenantLimits> GetTenantLimitsAsync();

    /// <summary>
    /// Atualiza configurações de limite do tenant
    /// </summary>
    Task<TenantResult> UpdateTenantLimitsAsync(UpdateTenantLimitsRequest request);
}

/// <summary>
/// Resultado de operações de tenant
/// </summary>
public class TenantResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public Tenant? Tenant { get; set; }
}

/// <summary>
/// Request para atualização de configurações do tenant
/// </summary>
public class UpdateTenantSettingsRequest
{
    public string Name { get; set; } = string.Empty;
    public string? Settings { get; set; }
}

/// <summary>
/// Request para atualização de limites do tenant
/// </summary>
public class UpdateTenantLimitsRequest
{
    public int? MaxUsers { get; set; }
    public int? MaxProjects { get; set; }
    public DateTime? SubscriptionExpiresAt { get; set; }
}

/// <summary>
/// Estatísticas do tenant
/// </summary>
public class TenantStatistics
{
    public int TotalUsers { get; set; }
    public int ActiveUsers { get; set; }
    public int TotalProjects { get; set; }
    public int ActiveProjects { get; set; }
    public DateTime? LastUserLogin { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// Limites do tenant
/// </summary>
public class TenantLimits
{
    public int? MaxUsers { get; set; }
    public int? MaxProjects { get; set; }
    public int CurrentUsers { get; set; }
    public int CurrentProjects { get; set; }
    public DateTime? SubscriptionExpiresAt { get; set; }
    public bool IsSubscriptionExpired => SubscriptionExpiresAt.HasValue && SubscriptionExpiresAt.Value < DateTime.UtcNow;
    public bool IsUserLimitReached => MaxUsers.HasValue && CurrentUsers >= MaxUsers.Value;
    public bool IsProjectLimitReached => MaxProjects.HasValue && CurrentProjects >= MaxProjects.Value;
}
