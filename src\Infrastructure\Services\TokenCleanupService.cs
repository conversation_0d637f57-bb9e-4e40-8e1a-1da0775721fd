using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Serviço em background para limpeza de tokens expirados
/// </summary>
public class TokenCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<TokenCleanupService> _logger;
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(6); // Executa a cada 6 horas

    public TokenCleanupService(
        IServiceProvider serviceProvider,
        ILogger<TokenCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Token Cleanup Service iniciado");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupExpiredTokensAsync();
                await Task.Delay(_cleanupInterval, stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Esperado quando o serviço é cancelado
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro durante a limpeza de tokens expirados");
                // Aguarda um tempo menor em caso de erro antes de tentar novamente
                await Task.Delay(TimeSpan.FromMinutes(30), stoppingToken);
            }
        }

        _logger.LogInformation("Token Cleanup Service finalizado");
    }

    private async Task CleanupExpiredTokensAsync()
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var tokenService = scope.ServiceProvider.GetRequiredService<IPasswordResetTokenService>();

            var deletedCount = await tokenService.CleanupExpiredTokensAsync();

            if (deletedCount > 0)
            {
                _logger.LogInformation("Limpeza de tokens concluída. {Count} tokens expirados removidos", deletedCount);
            }
            else
            {
                _logger.LogDebug("Limpeza de tokens concluída. Nenhum token expirado encontrado");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar limpeza de tokens expirados");
            throw;
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Token Cleanup Service está sendo finalizado...");
        await base.StopAsync(cancellationToken);
    }
}
