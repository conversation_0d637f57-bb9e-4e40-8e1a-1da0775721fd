using Microsoft.AspNetCore.SignalR;
using BOS.Plant.Modules.OpcDa.Interfaces;
using BOS.Plant.Modules.OpcDa.Models;
using BOS.Plant.Modules.OpcDa.DTOs;
using BOS.Plant.Dashboard.Hubs;

namespace BOS.Plant.Dashboard.Services;

/// <summary>
/// Serviço para transmitir dados do OpcDaService via SignalR
/// </summary>
public class OpcDataBroadcastService : BackgroundService
{
    private readonly IOpcDaService _opcDaService;
    private readonly IHubContext<OpcDataHub> _hubContext;
    private readonly ILogger<OpcDataBroadcastService> _logger;
    private Timer? _dataTimer;
    private Timer? _statusTimer;

    public OpcDataBroadcastService(
        IOpcDaService opcDaService,
        IHubContext<OpcDataHub> hubContext,
        ILogger<OpcDataBroadcastService> logger)
    {
        _opcDaService = opcDaService;
        _hubContext = hubContext;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("=== OpcDataBroadcastService iniciado ===");

        // Configurar eventos do OpcDaService para dados REAIS
        _opcDaService.DataUpdated += OnDataUpdated;
        _opcDaService.ConnectionStatusChanged += OnConnectionStatusChanged;

        // Timer para enviar status a cada 10 segundos (mantém apenas status, remove dados simulados)
        _statusTimer = new Timer(BroadcastStatus, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(10));

        _logger.LogInformation("✅ OpcDataBroadcastService configurado para usar APENAS dados reais do OpcDaService");
        _logger.LogInformation("❌ Dados simulados DESABILITADOS - apenas tags reais adicionados serão monitorados");

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("OpcDataBroadcastService cancelado");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no OpcDataBroadcastService");
        }
        finally
        {
            _dataTimer?.Dispose();
            _statusTimer?.Dispose();
            _opcDaService.DataUpdated -= OnDataUpdated;
            _opcDaService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            _logger.LogInformation("=== OpcDataBroadcastService finalizado ===");
        }
    }

    private async void OnDataUpdated(object? sender, OpcDataUpdateEventArgs e)
    {
        try
        {
            var dto = new OpcDataUpdateDto
            {
                TagName = e.Item.TagName,
                Value = e.Item.Value,
                Quality = e.Item.Quality.ToString(),
                Timestamp = e.Timestamp,
                DataType = e.Item.DataType?.Name ?? "Unknown",
                GroupName = e.Item.GroupName ?? "Default"
            };

            await _hubContext.Clients.All.SendAsync("DataUpdate", dto);
            _logger.LogDebug("Dados transmitidos via SignalR: {TagName} = {Value}", e.Item.TagName, e.Item.Value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao transmitir dados via SignalR");
        }
    }

    private async void OnConnectionStatusChanged(object? sender, BOS.Plant.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs e)
    {
        try
        {
            var dto = new OpcConnectionStatusDto
            {
                Status = e.Status.ToString(),
                Timestamp = DateTime.UtcNow,
                Message = e.Message,
                TotalItems = 0,
                ActiveItems = 0
            };

            await _hubContext.Clients.All.SendAsync("ConnectionStatusChanged", dto);
            _logger.LogInformation("Status de conexão transmitido via SignalR: {Status}", e.Status);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao transmitir status via SignalR");
        }
    }



    private async void BroadcastStatus(object? state)
    {
        try
        {
            // Enviar status de conexão REAL
            var status = _opcDaService.GetConnectionStatus();
            var statistics = await _opcDaService.GetStatisticsAsync();
            var allItems = await _opcDaService.GetAllItemsAsync();

            var statusDto = new OpcConnectionStatusDto
            {
                Status = status.ToString(),
                Timestamp = DateTime.UtcNow,
                TotalItems = allItems?.Count() ?? 0,
                ActiveItems = allItems?.Count(item => item.IsActive) ?? 0
            };

            await _hubContext.Clients.All.SendAsync("ConnectionStatusUpdate", statusDto);

            // Enviar estatísticas REAIS do OpcDaService
            if (statistics != null)
            {
                await _hubContext.Clients.All.SendAsync("StatisticsReceived", statistics);
            }

            _logger.LogDebug("Status e estatísticas REAIS transmitidos via SignalR");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao transmitir status e estatísticas");
        }
    }
}
