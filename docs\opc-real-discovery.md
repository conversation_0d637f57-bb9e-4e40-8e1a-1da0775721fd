# Descoberta Real de Servidores OPC DA

Este documento descreve a implementação da descoberta real de servidores OPC DA, substituindo a lista hardcoded por uma busca dinâmica no sistema.

## 🔍 **Problema Identificado**

A implementação anterior estava retornando apenas uma lista estática de servidores OPC comuns, não realizando uma descoberta real. Isso limitava a funcionalidade e não refletia os servidores realmente instalados no sistema.

## ✅ **Solução Implementada**

### **1. Descoberta via Registro do Windows**

Implementada busca no registro `HKEY_CLASSES_ROOT` para encontrar servidores OPC registrados:

```csharp
private async Task<List<OpcServerInfo>> DiscoverRegisteredOpcServersAsync(string host)
{
    // Buscar no registro HKEY_CLASSES_ROOT por servidores OPC
    using var classesRoot = Registry.ClassesRoot;
    var progIds = FindOpcProgIds(classesRoot);
    
    foreach (var progId in progIds)
    {
        var serverInfo = CreateServerInfoFromProgId(progId, host);
        if (serverInfo != null)
        {
            servers.Add(serverInfo);
        }
    }
}
```

### **2. Busca Inteligente de ProgIDs**

Algoritmo que identifica ProgIDs relacionados a OPC:

```csharp
private List<string> FindOpcProgIds(RegistryKey classesRoot)
{
    foreach (var keyName in classesRoot.GetSubKeyNames())
    {
        // Buscar por chaves que contenham "OPC" no nome
        if (keyName.Contains("OPC", StringComparison.OrdinalIgnoreCase) ||
            keyName.Contains("Matrikon", StringComparison.OrdinalIgnoreCase) ||
            keyName.Contains("KEPware", StringComparison.OrdinalIgnoreCase) ||
            // ... outros fornecedores conhecidos
           )
        {
            // Verificar se tem CLSID (indicando que é um componente COM)
            using var clsidKey = subKey.OpenSubKey("CLSID");
            if (clsidKey != null)
            {
                progIds.Add(keyName);
            }
        }
    }
}
```

### **3. Verificação Real de Disponibilidade**

Teste efetivo se o servidor OPC está funcionando:

```csharp
private async Task<bool> CheckServerAvailability(string progId)
{
    try
    {
        // Tentar obter o tipo COM do ProgID
        var serverType = Type.GetTypeFromProgID(progId);
        if (serverType == null) return false;

        // Tentar criar uma instância do servidor
        var serverInstance = Activator.CreateInstance(serverType);
        if (serverInstance != null)
        {
            // Liberar a instância imediatamente
            if (Marshal.IsComObject(serverInstance))
            {
                Marshal.ReleaseComObject(serverInstance);
            }
            return true;
        }
        return false;
    }
    catch (COMException)
    {
        // Servidor registrado mas não disponível
        return false;
    }
}
```

### **4. Cache Inteligente**

Sistema de cache para evitar descobertas desnecessárias:

```csharp
public async Task<IEnumerable<OpcServerInfo>> DiscoverServersAsync(string host = "localhost")
{
    // Verificar cache primeiro
    lock (_cacheLock)
    {
        if (_serverCache.TryGetValue(host, out var cachedServers))
        {
            var cacheAge = DateTime.UtcNow - cachedServers.FirstOrDefault()?.LastChecked;
            if (cacheAge?.TotalMinutes < 5) // Cache válido por 5 minutos
            {
                return cachedServers;
            }
        }
    }
    
    return await RefreshServerDiscoveryAsync(host, cancellationToken);
}
```

### **5. Descoberta em Rede**

Busca paralela em múltiplos hosts:

```csharp
public async Task<IEnumerable<OpcServerInfo>> DiscoverServersInNetworkAsync(IEnumerable<string> hosts)
{
    var tasks = new List<Task<IEnumerable<OpcServerInfo>>>();

    // Executar descoberta em paralelo para todos os hosts
    foreach (var host in hosts)
    {
        tasks.Add(DiscoverServersAsync(host, cancellationToken));
    }

    // Aguardar todas as descobertas
    var results = await Task.WhenAll(tasks);

    // Consolidar resultados
    var allServers = new List<OpcServerInfo>();
    foreach (var serverList in results)
    {
        allServers.AddRange(serverList);
    }

    return allServers.OrderBy(s => s.Host).ThenBy(s => s.DisplayName);
}
```

### **6. Extração de Metadados**

Obtenção de informações detalhadas do registro:

```csharp
private OpcServerInfo? CreateServerInfoFromProgId(string progId, string host)
{
    using var progIdKey = classesRoot.OpenSubKey(progId);
    
    var displayName = progIdKey.GetValue("")?.ToString() ?? progId;
    
    // Tentar extrair informações adicionais via CLSID
    using var clsidKey = progIdKey.OpenSubKey("CLSID");
    if (clsidKey != null)
    {
        var clsid = clsidKey.GetValue("")?.ToString();
        // Obter nome e versão do CLSID
    }
    
    // Determinar vendor baseado no ProgID
    var vendor = DetermineVendorFromProgId(progId);
    
    return new OpcServerInfo
    {
        ProgId = progId,
        DisplayName = displayName,
        Vendor = vendor,
        Host = host,
        IsAvailable = await CheckServerAvailability(progId)
    };
}
```

## 🎯 **Funcionalidades da Interface**

### **1. Campo de Host Personalizado**

```html
<MudTextField @bind-Value="_hostToDiscover" 
             Label="Host" 
             Placeholder="localhost"
             Adornment="Adornment.Start"
             AdornmentIcon="@Icons.Material.Filled.Computer" />
```

### **2. Descoberta Local e em Rede**

- **Botão "Descobrir"**: Busca no host especificado
- **Botão "Rede Local"**: Busca em IPs comuns da rede local

### **3. Informações Detalhadas**

Tabela expandida com:
- Status (Disponível/Indisponível/Conectado)
- Nome do Servidor
- Host
- ProgID
- Fornecedor
- Versão
- Última Verificação
- Ações

## 📊 **Benefícios da Nova Implementação**

### **1. Descoberta Real**
- ✅ Busca servidores realmente instalados no sistema
- ✅ Verifica disponibilidade efetiva
- ✅ Extrai metadados do registro

### **2. Performance**
- ✅ Cache inteligente (5 minutos)
- ✅ Descoberta paralela em rede
- ✅ Verificação assíncrona

### **3. Flexibilidade**
- ✅ Busca em hosts remotos
- ✅ Descoberta em rede local
- ✅ Fallback para servidores conhecidos

### **4. Robustez**
- ✅ Tratamento de erros COM
- ✅ Liberação adequada de recursos
- ✅ Logs detalhados para debug

## 🔧 **Fornecedores Suportados**

O sistema identifica automaticamente servidores de:

- **Matrikon** - Servidores de simulação e industriais
- **PTC (KEPware)** - KEPServerEX
- **Siemens** - SIMATIC NET OPC Server
- **Rockwell Automation** - RSLinx OPC Server
- **Wonderware** - SuiteLink
- **Schneider Electric** - OFS (OPC Factory Server)
- **National Instruments** - NI OPC Server
- **GE Fanuc** - Servidores industriais
- **Honeywell** - Servidores de processo

## 🚀 **Como Usar**

### **1. Descoberta Local:**
```
1. Deixar campo Host como "localhost"
2. Clicar "Descobrir"
3. Ver servidores instalados localmente
```

### **2. Descoberta Remota:**
```
1. Inserir IP do host remoto (ex: *************)
2. Clicar "Descobrir"
3. Ver servidores no host especificado
```

### **3. Descoberta em Rede:**
```
1. Clicar "Rede Local"
2. Sistema busca em IPs comuns (***********-10, ***********-10)
3. Ver todos servidores encontrados na rede
```

## 📋 **Logs de Debug**

O sistema gera logs detalhados:

```
[DBG] Buscando servidores OPC no registro do Windows para host: localhost
[DBG] Encontrado ProgID OPC: Matrikon.OPC.Simulation.1
[DBG] Encontrados 3 servidores OPC registrados
[INF] Encontrados 5 servidores OPC no host 'localhost' (2 disponíveis)
```

## ⚠️ **Limitações e Considerações**

### **1. Segurança**
- Busca no registro requer permissões adequadas
- Hosts remotos podem ter firewall/restrições

### **2. Performance**
- Verificação COM pode ser lenta para servidores indisponíveis
- Cache minimiza impacto de descobertas repetidas

### **3. Compatibilidade**
- Funciona apenas no Windows (registro e COM)
- Requer .NET Framework ou .NET 5+ com suporte Windows

## 🔄 **Próximas Melhorias**

1. **Descoberta DCOM**: Busca em hosts remotos via DCOM
2. **OPC Core Components**: Usar APIs nativas do OPC
3. **Descoberta Automática**: Scan automático da rede
4. **Filtros Avançados**: Filtrar por fornecedor, versão, etc.
5. **Histórico**: Manter histórico de servidores descobertos

A nova implementação transforma a descoberta de servidores OPC de uma lista estática em um sistema dinâmico e inteligente que reflete a realidade do ambiente!
