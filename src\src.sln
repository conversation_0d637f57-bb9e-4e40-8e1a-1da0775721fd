Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Application", "Application\BOS.Plant.Application.csproj", "{12D5E4DA-55A1-6B7F-6334-503AEFF42B3A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Dashboard", "BOS.Plant.Dashboard\BOS.Plant.Dashboard.csproj", "{7AA2B536-6D9B-33FB-7D5B-00B252DA43E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Core", "Core\BOS.Plant.Core.csproj", "{17224083-B5A7-7433-75AA-0B8C1757A1AC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Infrastructure", "Infrastructure\BOS.Plant.Infrastructure.csproj", "{6CE77204-57A3-9187-AD9A-3C6791D685A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Shared", "Shared\BOS.Plant.Shared.csproj", "{DC28221D-41DA-B750-5DCB-06E58B7C714D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modules", "Modules", "{CA253913-39DE-BFD0-C9A3-4B7EC6FBDF17}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Modules.Auth", "Modules\Auth\BOS.Plant.Modules.Auth.csproj", "{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Modules.Projects", "Modules\Projects\BOS.Plant.Modules.Projects.csproj", "{AD036A0D-9975-1797-F23E-5C0522474308}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Modules.Tenants", "Modules\Tenants\BOS.Plant.Modules.Tenants.csproj", "{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "BOS.Plant.Modules.Users", "Modules\Users\BOS.Plant.Modules.Users.csproj", "{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{12D5E4DA-55A1-6B7F-6334-503AEFF42B3A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{12D5E4DA-55A1-6B7F-6334-503AEFF42B3A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{12D5E4DA-55A1-6B7F-6334-503AEFF42B3A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{12D5E4DA-55A1-6B7F-6334-503AEFF42B3A}.Release|Any CPU.Build.0 = Release|Any CPU
		{7AA2B536-6D9B-33FB-7D5B-00B252DA43E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AA2B536-6D9B-33FB-7D5B-00B252DA43E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AA2B536-6D9B-33FB-7D5B-00B252DA43E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AA2B536-6D9B-33FB-7D5B-00B252DA43E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{17224083-B5A7-7433-75AA-0B8C1757A1AC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{17224083-B5A7-7433-75AA-0B8C1757A1AC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{17224083-B5A7-7433-75AA-0B8C1757A1AC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{17224083-B5A7-7433-75AA-0B8C1757A1AC}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CE77204-57A3-9187-AD9A-3C6791D685A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CE77204-57A3-9187-AD9A-3C6791D685A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CE77204-57A3-9187-AD9A-3C6791D685A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CE77204-57A3-9187-AD9A-3C6791D685A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{DC28221D-41DA-B750-5DCB-06E58B7C714D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC28221D-41DA-B750-5DCB-06E58B7C714D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC28221D-41DA-B750-5DCB-06E58B7C714D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC28221D-41DA-B750-5DCB-06E58B7C714D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD}.Release|Any CPU.Build.0 = Release|Any CPU
		{AD036A0D-9975-1797-F23E-5C0522474308}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AD036A0D-9975-1797-F23E-5C0522474308}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AD036A0D-9975-1797-F23E-5C0522474308}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AD036A0D-9975-1797-F23E-5C0522474308}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{7B3A3B24-3DFE-F5CE-C2A1-0BFE06B7BEAD} = {CA253913-39DE-BFD0-C9A3-4B7EC6FBDF17}
		{AD036A0D-9975-1797-F23E-5C0522474308} = {CA253913-39DE-BFD0-C9A3-4B7EC6FBDF17}
		{FF288893-7EF9-4A46-83F7-5B6FCF4ABFB7} = {CA253913-39DE-BFD0-C9A3-4B7EC6FBDF17}
		{BB1F146F-FC96-DAAB-BFD2-7F6C20A4F6C2} = {CA253913-39DE-BFD0-C9A3-4B7EC6FBDF17}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E2250884-BCDD-442F-9C1D-937194C72204}
	EndGlobalSection
EndGlobal
