@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <MudContainer Style="max-width: 500px;">
            <MudText Typo="Typo.h6" Class="mb-4">Criar Novo Grupo</MudText>
            
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_groupName" 
                                Label="Nome do Grupo" 
                                Variant="Variant.Outlined"
                                Required="true"
                                RequiredError="Nome do grupo é obrigatório"
                                HelperText="Nome único para identificar o grupo de tags" />
                </MudItem>
                <MudItem xs="12">
                    <MudNumericField @bind-Value="_updateRate" 
                                   Label="Taxa de Atualização (ms)" 
                                   Variant="Variant.Outlined"
                                   Min="100"
                                   Max="60000"
                                   Step="100"
                                   HelperText="Intervalo entre atualizações dos dados (100ms - 60s)" />
                </MudItem>
                <MudItem xs="12">
                    <MudTextField @bind-Value="_description" 
                                Label="Descrição (Opcional)" 
                                Variant="Variant.Outlined"
                                Lines="3"
                                HelperText="Descrição opcional do propósito do grupo" />
                </MudItem>
            </MudGrid>

            <!-- Configurações Avançadas -->
            <MudExpansionPanels Class="mt-4">
                <MudExpansionPanel Text="Configurações Avançadas">
                    <MudGrid>
                        <MudItem xs="12">
                            <MudSwitch @bind-Value="_isActive" 
                                     Label="Grupo Ativo" 
                                     Color="Color.Primary"
                                     UnCheckedColor="Color.Default" />
                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                Grupos ativos recebem atualizações automaticamente
                            </MudText>
                        </MudItem>
                    </MudGrid>
                </MudExpansionPanel>
            </MudExpansionPanels>

            <!-- Sugestões de Nomes -->
            <MudCard Elevation="1" Class="mt-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.subtitle2">Sugestões de Nomes</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="d-flex flex-wrap gap-2">
                        @foreach (var suggestion in _nameSuggestions)
                        {
                            <MudChip T="string" 
                                   Size="Size.Small" 
                                   Color="Color.Primary" 
                                   Variant="Variant.Outlined"
                                   OnClick="@(() => _groupName = suggestion)"
                                   Style="cursor: pointer;">
                                @suggestion
                            </MudChip>
                        }
                    </div>
                </MudCardContent>
            </MudCard>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel">Cancelar</MudButton>
        <MudButton Color="Color.Primary" 
                  Variant="Variant.Filled" 
                  OnClick="Submit"
                  Disabled="string.IsNullOrWhiteSpace(_groupName)">
            Criar Grupo
        </MudButton>
    </DialogActions>
</MudDialog>

@code {
    [CascadingParameter]
    IMudDialogInstance MudDialog { get; set; } = null!;

    private string _groupName = string.Empty;
    private int _updateRate = 1000;
    private string _description = string.Empty;
    private bool _isActive = true;

    private readonly string[] _nameSuggestions = new[]
    {
        "ProcessData",
        "AlarmData", 
        "MotorData",
        "QualityData",
        "TemperatureData",
        "PressureData",
        "FlowData",
        "LevelData",
        "AnalogInputs",
        "DigitalInputs",
        "AnalogOutputs",
        "DigitalOutputs",
        "SafetyData",
        "ProductionData",
        "MaintenanceData"
    };

    private void Submit()
    {
        if (string.IsNullOrWhiteSpace(_groupName))
        {
            Snackbar.Add("Nome do grupo é obrigatório", Severity.Error);
            return;
        }

        // Validar nome do grupo
        if (_groupName.Length < 3)
        {
            Snackbar.Add("Nome do grupo deve ter pelo menos 3 caracteres", Severity.Error);
            return;
        }

        if (!IsValidGroupName(_groupName))
        {
            Snackbar.Add("Nome do grupo contém caracteres inválidos", Severity.Error);
            return;
        }

        var result = (_groupName.Trim(), _updateRate);
        MudDialog.Close(DialogResult.Ok(result));
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private bool IsValidGroupName(string name)
    {
        // Verificar se o nome contém apenas caracteres válidos
        return name.All(c => char.IsLetterOrDigit(c) || c == '_' || c == '-' || c == '.');
    }
}
