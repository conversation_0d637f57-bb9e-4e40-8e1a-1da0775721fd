using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.Extensions.Logging;
using BOS.Plant.Modules.OpcDa.DTOs;

namespace BOS.Plant.Dashboard.Services;

/// <summary>
/// Cliente SignalR para comunicação com o serviço OPC DA
/// </summary>
public class OpcSignalRClient : IAsyncDisposable
{
    private readonly ILogger<OpcSignalRClient> _logger;
    private readonly IConfiguration _configuration;
    private HubConnection? _connection;
    private readonly string _hubUrl;

    // Eventos para notificar componentes Blazor
    public event EventHandler<OpcDataUpdateDto>? DataReceived;
    public event EventHandler<OpcConnectionStatusDto>? ConnectionStatusChanged;
    public event EventHandler<OpcServerStatisticsDto>? StatisticsReceived;
    public event EventHandler<List<OpcDataUpdateDto>>? AllItemsReceived;
    public event EventHandler<string>? ErrorReceived;

    public OpcSignalRClient(ILogger<OpcSignalRClient> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        string configUrl = _configuration.GetValue<string>("OpcService:HubUrl") ?? "http://localhost:5000/opcHub";
        
        // Substitui '*' por 'localhost' na URL do hub para conexões de cliente
        _hubUrl = configUrl.Replace("*", "localhost");
    }

    /// <summary>
    /// Conecta ao hub SignalR
    /// </summary>
    public async Task ConnectAsync()
    {
        try
        {
            _logger.LogInformation("Iniciando conexão com hub OPC SignalR: {HubUrl}", _hubUrl);

            _connection = new HubConnectionBuilder()
                .WithUrl(_hubUrl)
                .WithAutomaticReconnect(new[] { TimeSpan.Zero, TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(30) })
                .Build();

            // Configurar handlers para eventos do hub
            ConfigureEventHandlers();

            _logger.LogDebug("Tentando estabelecer conexão SignalR...");
            await _connection.StartAsync();
            _logger.LogInformation("Conectado ao hub OPC SignalR com sucesso. ConnectionId: {ConnectionId}", _connection.ConnectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao conectar ao hub OPC SignalR: {HubUrl}", _hubUrl);
            throw;
        }
    }

    /// <summary>
    /// Desconecta do hub SignalR
    /// </summary>
    public async Task DisconnectAsync()
    {
        if (_connection != null)
        {
            await _connection.DisposeAsync();
            _connection = null;
            _logger.LogInformation("Desconectado do hub OPC SignalR");
        }
    }

    /// <summary>
    /// Inscreve-se em um grupo específico
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    public async Task JoinGroupAsync(string groupName)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("JoinGroup", groupName);
            _logger.LogInformation("Inscrito no grupo: {GroupName}", groupName);
        }
    }

    /// <summary>
    /// Remove inscrição de um grupo
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    public async Task LeaveGroupAsync(string groupName)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            await _connection.InvokeAsync("LeaveGroup", groupName);
            _logger.LogInformation("Removido do grupo: {GroupName}", groupName);
        }
    }

    /// <summary>
    /// Solicita leitura de um item específico
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    public async Task RequestItemReadAsync(string tagName)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            _logger.LogDebug("Solicitando leitura do item: {TagName}", tagName);
            await _connection.InvokeAsync("RequestItemRead", tagName);
        }
        else
        {
            _logger.LogWarning("Tentativa de leitura de item {TagName} com conexão não estabelecida. Estado: {State}", tagName, _connection?.State);
        }
    }

    /// <summary>
    /// Solicita escrita em um item
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    /// <param name="value">Valor a ser escrito</param>
    public async Task RequestItemWriteAsync(string tagName, object value)
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            _logger.LogDebug("Solicitando escrita no item: {TagName} = {Value}", tagName, value);
            await _connection.InvokeAsync("RequestItemWrite", tagName, value);
        }
        else
        {
            _logger.LogWarning("Tentativa de escrita no item {TagName} com conexão não estabelecida. Estado: {State}", tagName, _connection?.State);
        }
    }

    /// <summary>
    /// Solicita estatísticas do servidor
    /// </summary>
    public async Task RequestStatisticsAsync()
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            _logger.LogDebug("Solicitando estatísticas do servidor OPC");
            await _connection.InvokeAsync("RequestStatistics");
        }
        else
        {
            _logger.LogDebug("Tentativa de solicitar estatísticas com conexão não estabelecida. Estado: {State}", _connection?.State);
        }
    }

    /// <summary>
    /// Solicita todos os itens monitorados
    /// </summary>
    public async Task RequestAllItemsAsync()
    {
        if (_connection?.State == HubConnectionState.Connected)
        {
            _logger.LogDebug("Solicitando todos os itens monitorados");
            await _connection.InvokeAsync("RequestAllItems");
        }
        else
        {
            _logger.LogDebug("Tentativa de solicitar todos os itens com conexão não estabelecida. Estado: {State}", _connection?.State);
        }
    }

    /// <summary>
    /// Verifica se está conectado
    /// </summary>
    public bool IsConnected => _connection?.State == HubConnectionState.Connected;

    /// <summary>
    /// Obtém o estado atual da conexão
    /// </summary>
    public HubConnectionState ConnectionState => _connection?.State ?? HubConnectionState.Disconnected;

    private void ConfigureEventHandlers()
    {
        if (_connection == null) return;

        // Handler para atualizações de dados
        _connection.On<OpcDataUpdateDto>("DataUpdate", (data) =>
        {
            _logger.LogDebug("Dados recebidos: {TagName} = {Value}", data.TagName, data.Value);
            DataReceived?.Invoke(this, data);
        });

        // Handler para atualizações de dados de grupo
        _connection.On<OpcDataUpdateDto>("GroupDataUpdate", (data) =>
        {
            _logger.LogDebug("Dados de grupo recebidos: {TagName} = {Value}", data.TagName, data.Value);
            DataReceived?.Invoke(this, data);
        });

        // Handler para mudanças de status de conexão
        _connection.On<OpcConnectionStatusDto>("ConnectionStatusUpdate", (status) =>
        {
            _logger.LogInformation("Status de conexão OPC: {Status}", status.Status);
            ConnectionStatusChanged?.Invoke(this, status);
        });

        // Handler para atualizações de item individual
        _connection.On<OpcDataUpdateDto>("ItemDataUpdate", (data) =>
        {
            _logger.LogDebug("Atualização de item: {TagName} = {Value}", data.TagName, data.Value);
            DataReceived?.Invoke(this, data);
        });

        // Handler para estatísticas
        _connection.On<OpcServerStatisticsDto>("StatisticsUpdate", (stats) =>
        {
            _logger.LogDebug("Estatísticas recebidas: {TotalUpdates} atualizações", stats.TotalUpdates);
            StatisticsReceived?.Invoke(this, stats);
        });

        // Handler para todos os itens
        _connection.On<List<OpcDataUpdateDto>>("AllItemsUpdate", (items) =>
        {
            _logger.LogDebug("Recebidos {Count} itens", items.Count);
            AllItemsReceived?.Invoke(this, items);
        });

        // Handlers para erros
        _connection.On<string, string>("ItemReadError", (tagName, error) =>
        {
            _logger.LogError("Erro ao ler item {TagName}: {Error}", tagName, error);
            ErrorReceived?.Invoke(this, $"Erro ao ler {tagName}: {error}");
        });

        _connection.On<string, string>("ItemWriteError", (tagName, error) =>
        {
            _logger.LogError("Erro ao escrever item {TagName}: {Error}", tagName, error);
            ErrorReceived?.Invoke(this, $"Erro ao escrever {tagName}: {error}");
        });

        _connection.On<string>("StatisticsError", (error) =>
        {
            _logger.LogError("Erro ao obter estatísticas: {Error}", error);
            ErrorReceived?.Invoke(this, $"Erro nas estatísticas: {error}");
        });

        _connection.On<string>("AllItemsError", (error) =>
        {
            _logger.LogError("Erro ao obter todos os itens: {Error}", error);
            ErrorReceived?.Invoke(this, $"Erro ao obter itens: {error}");
        });

        // Handler para confirmações
        _connection.On<string>("GroupJoined", (groupName) =>
        {
            _logger.LogInformation("Confirmação: inscrito no grupo {GroupName}", groupName);
        });

        _connection.On<string>("GroupLeft", (groupName) =>
        {
            _logger.LogInformation("Confirmação: removido do grupo {GroupName}", groupName);
        });

        _connection.On<string, bool>("ItemWriteResult", (tagName, success) =>
        {
            if (success)
            {
                _logger.LogInformation("Item {TagName} escrito com sucesso", tagName);
            }
            else
            {
                _logger.LogWarning("Falha ao escrever item {TagName}", tagName);
            }
        });

        // Handler para reconexão
        _connection.Reconnecting += (error) =>
        {
            _logger.LogWarning(error, "Reconectando ao hub OPC SignalR...");
            return Task.CompletedTask;
        };

        _connection.Reconnected += (connectionId) =>
        {
            _logger.LogInformation("Reconectado ao hub OPC SignalR com sucesso. Nova ConnectionId: {ConnectionId}", connectionId);
            return Task.CompletedTask;
        };

        _connection.Closed += (error) =>
        {
            if (error != null)
            {
                _logger.LogError(error, "Conexão com hub OPC SignalR fechada com erro");
            }
            else
            {
                _logger.LogInformation("Conexão com hub OPC SignalR fechada normalmente");
            }
            return Task.CompletedTask;
        };
    }

    public async ValueTask DisposeAsync()
    {
        await DisconnectAsync();
    }
}
