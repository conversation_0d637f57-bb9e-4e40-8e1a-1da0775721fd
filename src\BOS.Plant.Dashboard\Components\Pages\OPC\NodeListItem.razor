@using BOS.Plant.Modules.OpcDa.Models

@if (Node.IsBranch)
{
    <MudListItem T="string" Text="@Node.Name" Icon="@Icons.Material.Filled.Folder" 
                IconColor="@Color.Warning" OnClickPreventDefault="true" 
                OnClick="@(() => OnNodeClick.InvokeAsync(Node))" 
                @bind-Expanded="@Node.IsExpanded">
        <NestedList>
            @if (Node.Children != null && Node.Children.Any())
            {
                @foreach (var child in Node.Children)
                {
                    <NodeListItem Node="@child" OnNodeClick="OnNodeClick" />
                }
            }
        </NestedList>
    </MudListItem>
}
else
{
    <MudListItem T="string" Text="@Node.Name" Icon="@Icons.Material.Filled.Tag" 
        IconColor="@Color.Info" OnClickPreventDefault="true" 
        OnClick="@(() => OnNodeClick.InvokeAsync(Node))" />
}

@code {
    [Parameter] public OpcTagNode Node { get; set; } = null!;
    [Parameter] public EventCallback<OpcTagNode> OnNodeClick { get; set; }
}