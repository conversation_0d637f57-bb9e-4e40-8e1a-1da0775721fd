using BOS.Plant.Core.Common;
using System.Linq.Expressions;

namespace BOS.Plant.Core.Interfaces;

/// <summary>
/// Interface de repositório para entidades que pertencem a um tenant
/// </summary>
/// <typeparam name="T">Tipo da entidade que implementa ITenantEntity</typeparam>
public interface ITenantRepository<T> : IRepository<T> where T : class, ITenantEntity
{
    /// <summary>
    /// Obtém entidades por tenant
    /// </summary>
    Task<IEnumerable<T>> GetByTenantAsync(string tenantId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém entidades por tenant com filtro
    /// </summary>
    Task<IEnumerable<T>> GetByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>> predicate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém uma entidade por tenant e ID
    /// </summary>
    Task<T?> GetByTenantAndIdAsync(string tenantId, string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém entidades paginadas por tenant
    /// </summary>
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedByTenantAsync(
        string tenantId,
        int page, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Conta entidades por tenant
    /// </summary>
    Task<int> CountByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>>? predicate = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se existe uma entidade por tenant
    /// </summary>
    Task<bool> ExistsByTenantAsync(
        string tenantId, 
        Expression<Func<T, bool>> predicate, 
        CancellationToken cancellationToken = default);
}
