using BOS.Plant.OpcDa.Service;
using BOS.Plant.Modules.OpcDa.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;

// Configurar Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File(
        path: "logs/opcda-service-.log",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 10,
        fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}")
    .CreateLogger();

try
{
    Log.Information("Iniciando serviço OPC DA");

    var builder = WebApplication.CreateBuilder(args);

    // Configurar Serilog
    builder.Host.UseSerilog();

    // Configurar como serviço Windows se necessário
    builder.Host.UseWindowsService();

    // Adicionar serviços OPC DA
    builder.Services.AddOpcDaServices(builder.Configuration);

    // Adicionar worker service
    builder.Services.AddHostedService<Worker>();

    var app = builder.Build();

    // Configurar SignalR
    app.UseOpcDaSignalR();

    // Configurar CORS para desenvolvimento
    if (app.Environment.IsDevelopment())
    {
        app.UseCors(policy =>
        {
            policy.AllowAnyOrigin()
                  .AllowAnyMethod()
                  .AllowAnyHeader();
        });
    }

    Log.Information("Serviço OPC DA iniciado em {Urls}", builder.Configuration["Urls"]);
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Erro fatal ao iniciar o serviço OPC DA");
}
finally
{
    Log.CloseAndFlush();
}
