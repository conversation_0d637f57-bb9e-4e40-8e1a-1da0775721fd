using BOS.Plant.Core.Interfaces;
using BOS.Plant.Dashboard.Components;
using BOS.Plant.Dashboard.Services;
using BOS.Plant.Infrastructure.Extensions;
using BOS.Plant.Infrastructure.Services;
using BOS.Plant.Modules.Auth.Services;
using BOS.Plant.Modules.Projects.Services;
using BOS.Plant.Modules.Users.Services;
using BOS.Plant.Modules.Tenants.Services;
using BOS.Plant.Modules.OpcDa.Interfaces;
using BOS.Plant.Modules.OpcDa.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server;
using MudBlazor.Services;
using Serilog;

// Configurar Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(new ConfigurationBuilder()
        .AddJsonFile("appsettings.json")
        .Build())
    .CreateLogger();

try
{
    Log.Information("Iniciando aplicação Blazor Dashboard");

    var builder = WebApplication.CreateBuilder(args);

    // Configurar Serilog
    builder.Host.UseSerilog();

// Add MudBlazor services
builder.Services.AddMudServices();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Add controllers for API endpoints
builder.Services.AddControllers();

// Add SignalR for OPC data streaming
builder.Services.AddSignalR();

// Add Authentication and Authorization
builder.Services.AddAuthentication("Cookies")
    .AddCookie("Cookies", options =>
    {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        options.ExpireTimeSpan = TimeSpan.FromHours(8);
        options.SlidingExpiration = true;
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
    });

builder.Services.AddAuthorization(options =>
{
    // Política para SuperAdmin
    options.AddPolicy("SuperAdmin", policy =>
        policy.RequireRole("SuperAdmin"));

    // Política para Admin (SuperAdmin ou Admin)
    options.AddPolicy("Admin", policy =>
        policy.RequireRole("SuperAdmin", "Admin"));

    // Política para Manager (SuperAdmin, Admin ou Manager)
    options.AddPolicy("Manager", policy =>
        policy.RequireRole("SuperAdmin", "Admin", "Manager"));

    // Política para usuários autenticados
    options.AddPolicy("User", policy =>
        policy.RequireAuthenticatedUser());
});

// Add Custom AuthenticationStateProvider for Blazor
builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthenticationStateProvider>();

// Before other service registrations
builder.Services.AddSingleton<ITenantContextFactory, TenantContextFactory>();

// Add Infrastructure services
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddMultiTenancy();

// Add Module services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IUserManagementService, UserManagementService>();
builder.Services.AddScoped<IRoleManagementService, RoleManagementService>();
builder.Services.AddScoped<IProjectService, ProjectService>();
builder.Services.AddScoped<ITenantManagementService, TenantManagementService>();

// Add OPC SignalR Client
builder.Services.AddSingleton<OpcSignalRClient>();

// Add OPC Services
builder.Services.AddSingleton<IOpcDaService, OpcDaService>();
builder.Services.AddSingleton<OpcRealBrowsingService>();
builder.Services.AddSingleton<IOpcGroupManagementService, OpcGroupManagementService>();
builder.Services.AddSingleton<IOpcDiscoveryService>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<OpcDiscoveryService>>();
    var opcService = provider.GetRequiredService<IOpcDaService>();
    var groupManagementService = provider.GetRequiredService<IOpcGroupManagementService>();
    var discoveryService = new OpcDiscoveryService(logger, opcService, groupManagementService);

    // Configurar o browsing service após criação para evitar dependência circular
    var browsingService = provider.GetRequiredService<OpcRealBrowsingService>();
    discoveryService.SetRealBrowsingService(browsingService);

    return discoveryService;
});

// Add OPC DA Hosted Service to keep the service active
builder.Services.AddHostedService<BOS.Plant.Dashboard.Services.OpcDaHostedService>();

// Add OPC Data Broadcast Service to stream data via SignalR
builder.Services.AddHostedService<BOS.Plant.Dashboard.Services.OpcDataBroadcastService>();

// Add OPC SignalR Client
builder.Services.AddSingleton<BOS.Plant.Dashboard.Services.OpcSignalRClient>();

    // Add HttpContextAccessor
    builder.Services.AddHttpContextAccessor();

    var app = builder.Build();

    Log.Information("Aplicação construída com sucesso");

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();

// Add tenant resolution middleware
app.UseTenantResolution();

// Map controllers
app.MapControllers();

// Map SignalR hubs
app.MapHub<BOS.Plant.Dashboard.Hubs.OpcDataHub>("/hubs/opcdata");

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

    Log.Information("Aplicação Blazor Dashboard iniciada");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Erro fatal ao iniciar a aplicação Blazor Dashboard");
}
finally
{
    Log.CloseAndFlush();
}
