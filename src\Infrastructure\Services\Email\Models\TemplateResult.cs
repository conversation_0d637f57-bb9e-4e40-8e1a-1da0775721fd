namespace BOS.Plant.Infrastructure.Services.Email.Models;

public class TemplateResult
{
    public bool Success { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }

    public static TemplateResult CreateSuccess(string content, string message = "Template renderizado com sucesso")
    {
        return new TemplateResult { Success = true, Content = content, Message = message };
    }

    public static TemplateResult CreateFailure(string message, Exception? exception = null)
    {
        return new TemplateResult { Success = false, Message = message, Exception = exception };
    }
}
