using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Net;

namespace BOS.Plant.Infrastructure.Middleware;

/// <summary>
/// Middleware para rate limiting de requisições
/// </summary>
public class RateLimitingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitingMiddleware> _logger;

    // Configurações de rate limiting
    private readonly Dictionary<string, RateLimitRule> _rules = new()
    {
        { "/forgot-password", new RateLimitRule { MaxRequests = 3, WindowMinutes = 60 } },
        { "/reset-password", new RateLimitRule { MaxRequests = 5, WindowMinutes = 60 } },
        { "/login", new RateLimitRule { MaxRequests = 10, WindowMinutes = 15 } }
    };

    public RateLimitingMiddleware(
        RequestDelegate next,
        IMemoryCache cache,
        ILogger<RateLimitingMiddleware> logger)
    {
        _next = next;
        _cache = cache;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant();
        
        if (path != null && _rules.ContainsKey(path))
        {
            var clientIp = GetClientIpAddress(context);
            var rule = _rules[path];
            
            if (!await IsRequestAllowedAsync(clientIp, path, rule))
            {
                _logger.LogWarning("Rate limit exceeded for IP {IpAddress} on path {Path}", clientIp, path);
                
                context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                context.Response.Headers.Add("Retry-After", (rule.WindowMinutes * 60).ToString());
                
                await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                return;
            }
        }

        await _next(context);
    }

    private async Task<bool> IsRequestAllowedAsync(string clientIp, string path, RateLimitRule rule)
    {
        var key = $"rate_limit:{clientIp}:{path}";
        var now = DateTime.UtcNow;
        var windowStart = now.AddMinutes(-rule.WindowMinutes);

        // Obter lista de timestamps das requisições
        var requests = _cache.Get<List<DateTime>>(key) ?? new List<DateTime>();

        // Remover requisições antigas (fora da janela de tempo)
        requests = requests.Where(r => r > windowStart).ToList();

        // Verificar se excedeu o limite
        if (requests.Count >= rule.MaxRequests)
        {
            return false;
        }

        // Adicionar a requisição atual
        requests.Add(now);

        // Atualizar cache
        var expiration = TimeSpan.FromMinutes(rule.WindowMinutes);
        _cache.Set(key, requests, expiration);

        return true;
    }

    private string GetClientIpAddress(HttpContext context)
    {
        // Verificar headers de proxy
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fallback para IP da conexão
        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

/// <summary>
/// Regra de rate limiting
/// </summary>
public class RateLimitRule
{
    /// <summary>
    /// Número máximo de requisições permitidas
    /// </summary>
    public int MaxRequests { get; set; }

    /// <summary>
    /// Janela de tempo em minutos
    /// </summary>
    public int WindowMinutes { get; set; }
}
