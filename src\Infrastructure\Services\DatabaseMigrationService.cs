using BOS.Plant.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace BOS.Plant.Infrastructure.Services;

/// <summary>
/// Serviço para aplicar migrações do banco de dados na inicialização
/// </summary>
public class DatabaseMigrationService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<DatabaseMigrationService> _logger;

    public DatabaseMigrationService(IServiceProvider serviceProvider, ILogger<DatabaseMigrationService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        try
        {
            _logger.LogInformation("Aplicando migrações do banco de dados...");
            
            // Aplicar migrações pendentes
            await context.Database.MigrateAsync(cancellationToken);
            
            _logger.LogInformation("Migrações aplicadas com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao aplicar migrações do banco de dados");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
