@page "/settings"
@inherits AdminPageBase
@inject ISnackbar Snackbar
@using BOS.Plant.Dashboard.Components.Shared
@using System.ComponentModel.DataAnnotations

<PageTitle>Configurações - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <MudText Typo="Typo.h4" Class="mb-4">Configurações</MudText>

    <MudGrid>
        <!-- Configurações de Aparência -->
        <MudItem xs="12" md="6">
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Aparência</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudSelect T="string" @bind-Value="_appearanceSettings.Theme" Label="Tema" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("light")">Claro</MudSelectItem>
                            <MudSelectItem T="string" Value="@("dark")">Escuro</MudSelectItem>
                            <MudSelectItem T="string" Value="@("auto")">Automático</MudSelectItem>
                        </MudSelect>

                        <MudSelect T="string" @bind-Value="_appearanceSettings.Language" Label="Idioma" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("pt-BR")">Português (Brasil)</MudSelectItem>
                            <MudSelectItem T="string" Value="@("en-US")">English (US)</MudSelectItem>
                            <MudSelectItem T="string" Value="@("es-ES")">Español</MudSelectItem>
                        </MudSelect>

                        <MudSelect T="string" @bind-Value="_appearanceSettings.DateFormat" Label="Formato de Data" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("dd/MM/yyyy")">DD/MM/AAAA</MudSelectItem>
                            <MudSelectItem T="string" Value="@("MM/dd/yyyy")">MM/DD/AAAA</MudSelectItem>
                            <MudSelectItem T="string" Value="@("yyyy-MM-dd")">AAAA-MM-DD</MudSelectItem>
                        </MudSelect>
                        
                        <MudSelect T="string" @bind-Value="_appearanceSettings.TimeFormat" Label="Formato de Hora" Variant="Variant.Outlined">
                            <MudSelectItem T="string" Value="@("24h")">24 horas</MudSelectItem>
                            <MudSelectItem T="string" Value="@("12h")">12 horas AM-PM</MudSelectItem>
                        </MudSelect>
                    </MudStack>
                    
                    <div class="d-flex justify-end mt-4">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   OnClick="SaveAppearanceSettings"
                                   Disabled="@_savingAppearance"
                                   StartIcon="@Icons.Material.Filled.Save">
                            @if (_savingAppearance)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                <MudText Class="ms-2">Salvando...</MudText>
                            }
                            else
                            {
                                <MudText>Salvar</MudText>
                            }
                        </MudButton>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Configurações de Notificação -->
        <MudItem xs="12" md="6">
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Notificações</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudSwitch @bind-Value="_notificationSettings.EmailNotifications"
                                   Label="Notificações por Email"
                                   Color="Color.Primary" />
                        
                        <MudSwitch @bind-Value="_notificationSettings.PushNotifications"
                                   Label="Notificações Push"
                                   Color="Color.Primary" />
                        
                        <MudSwitch @bind-Value="_notificationSettings.ProjectUpdates"
                                   Label="Atualizações de Projetos"
                                   Color="Color.Primary" />
                        
                        <MudSwitch @bind-Value="_notificationSettings.UserActivity"
                                   Label="Atividade de Usuários"
                                   Color="Color.Primary" />
                        
                        <MudSwitch @bind-Value="_notificationSettings.SystemAlerts"
                                   Label="Alertas do Sistema"
                                   Color="Color.Primary" />
                    </MudStack>
                    
                    <div class="d-flex justify-end mt-4">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   OnClick="SaveNotificationSettings"
                                   Disabled="@_savingNotifications"
                                   StartIcon="@Icons.Material.Filled.Save">
                            @if (_savingNotifications)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                <MudText Class="ms-2">Salvando...</MudText>
                            }
                            else
                            {
                                <MudText>Salvar</MudText>
                            }
                        </MudButton>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Configurações de Segurança -->
        <MudItem xs="12" md="6">
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Segurança</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudNumericField @bind-Value="_securitySettings.SessionTimeout"
                                         Label="Timeout de Sessão (minutos)"
                                         Variant="Variant.Outlined"
                                         Min="5"
                                         Max="480"
                                         HelperText="Tempo limite para sessões inativas" />
                        
                        <MudSwitch @bind-Value="_securitySettings.RequirePasswordChange"
                                   Label="Exigir Mudança de Senha Periódica"
                                   Color="Color.Primary" />
                        
                        <MudNumericField @bind-Value="_securitySettings.PasswordExpiryDays"
                                         Label="Dias para Expiração de Senha"
                                         Variant="Variant.Outlined"
                                         Min="30"
                                         Max="365"
                                         Disabled="@(!_securitySettings.RequirePasswordChange)"
                                         HelperText="Número de dias até a senha expirar" />
                        
                        <MudSwitch @bind-Value="_securitySettings.EnableAuditLog"
                                   Label="Habilitar Log de Auditoria"
                                   Color="Color.Primary" />
                        
                        <MudSwitch @bind-Value="_securitySettings.RequireTwoFactor"
                                   Label="Exigir Autenticação de Dois Fatores"
                                   Color="Color.Primary" />
                    </MudStack>
                    
                    <div class="d-flex justify-end mt-4">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   OnClick="SaveSecuritySettings"
                                   Disabled="@_savingSecurity"
                                   StartIcon="@Icons.Material.Filled.Save">
                            @if (_savingSecurity)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                <MudText Class="ms-2">Salvando...</MudText>
                            }
                            else
                            {
                                <MudText>Salvar</MudText>
                            }
                        </MudButton>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Configurações do Sistema -->
        <MudItem xs="12" md="6">
            <MudCard Elevation="2" Class="mb-4">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Sistema</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Spacing="3">
                        <MudNumericField @bind-Value="_systemSettings.DefaultPageSize"
                                         Label="Itens por Página (Padrão)"
                                         Variant="Variant.Outlined"
                                         Min="5"
                                         Max="100"
                                         HelperText="Número padrão de itens exibidos em listas" />
                        
                        <MudSwitch @bind-Value="_systemSettings.EnableAutoSave"
                                   Label="Salvamento Automático"
                                   Color="Color.Primary" />
                        
                        <MudNumericField @bind-Value="_systemSettings.AutoSaveInterval"
                                         Label="Intervalo de Salvamento (segundos)"
                                         Variant="Variant.Outlined"
                                         Min="30"
                                         Max="600"
                                         Disabled="@(!_systemSettings.EnableAutoSave)"
                                         HelperText="Frequência do salvamento automático" />
                        
                        <MudSwitch @bind-Value="_systemSettings.EnableDebugMode"
                                   Label="Modo de Debug"
                                   Color="Color.Warning" />
                        
                        <MudSwitch @bind-Value="_systemSettings.EnableMaintenanceMode"
                                   Label="Modo de Manutenção"
                                   Color="Color.Error" />
                    </MudStack>
                    
                    <div class="d-flex justify-end mt-4">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   OnClick="SaveSystemSettings"
                                   Disabled="@_savingSystem"
                                   StartIcon="@Icons.Material.Filled.Save">
                            @if (_savingSystem)
                            {
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                <MudText Class="ms-2">Salvando...</MudText>
                            }
                            else
                            {
                                <MudText>Salvar</MudText>
                            }
                        </MudButton>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Informações do Sistema -->
        <MudItem xs="12">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Informações do Sistema</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" md="3">
                            <MudText Typo="Typo.body2"><strong>Versão:</strong></MudText>
                            <MudText Typo="Typo.body2">1.0.0</MudText>
                        </MudItem>
                        <MudItem xs="12" md="3">
                            <MudText Typo="Typo.body2"><strong>Ambiente:</strong></MudText>
                            <MudText Typo="Typo.body2">Desenvolvimento</MudText>
                        </MudItem>
                        <MudItem xs="12" md="3">
                            <MudText Typo="Typo.body2"><strong>Última Atualização:</strong></MudText>
                            <MudText Typo="Typo.body2">@DateTime.Now.ToString("dd/MM/yyyy")</MudText>
                        </MudItem>
                        <MudItem xs="12" md="3">
                            <MudText Typo="Typo.body2"><strong>Uptime:</strong></MudText>
                            <MudText Typo="Typo.body2">24h 15m</MudText>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</MudContainer>

@code {
    private const string Dmy = "dd/MM/yyyy";
    private const string Mdy = "MM/dd/yyyy";
    private const string Ymd = "yyyy-MM-dd";

    private bool _savingAppearance = false;
    private bool _savingNotifications = false;
    private bool _savingSecurity = false;
    private bool _savingSystem = false;
    
    private AppearanceSettings _appearanceSettings = new();
    private NotificationSettings _notificationSettings = new();
    private SecuritySettings _securitySettings = new();
    private SystemSettings _systemSettings = new();

    protected override void OnInitialized()
    {
        LoadSettings();
    }

    private void LoadSettings()
    {
        // Carregar configurações salvas (simulado)
        _appearanceSettings = new AppearanceSettings
        {
            Theme = "dark",
            Language = "pt-BR",
            DateFormat = "dd/MM/yyyy",
            TimeFormat = "24h"
        };

        _notificationSettings = new NotificationSettings
        {
            EmailNotifications = true,
            PushNotifications = true,
            ProjectUpdates = true,
            UserActivity = false,
            SystemAlerts = true
        };

        _securitySettings = new SecuritySettings
        {
            SessionTimeout = 60,
            RequirePasswordChange = false,
            PasswordExpiryDays = 90,
            EnableAuditLog = true,
            RequireTwoFactor = false
        };

        _systemSettings = new SystemSettings
        {
            DefaultPageSize = 10,
            EnableAutoSave = true,
            AutoSaveInterval = 120,
            EnableDebugMode = false,
            EnableMaintenanceMode = false
        };
    }

    private async Task SaveAppearanceSettings()
    {
        _savingAppearance = true;
        try
        {
            // Simular salvamento
            await Task.Delay(1000);
            Snackbar.Add("Configurações de aparência salvas com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingAppearance = false;
        }
    }

    private async Task SaveNotificationSettings()
    {
        _savingNotifications = true;
        try
        {
            // Simular salvamento
            await Task.Delay(1000);
            Snackbar.Add("Configurações de notificação salvas com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingNotifications = false;
        }
    }

    private async Task SaveSecuritySettings()
    {
        _savingSecurity = true;
        try
        {
            // Simular salvamento
            await Task.Delay(1000);
            Snackbar.Add("Configurações de segurança salvas com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingSecurity = false;
        }
    }

    private async Task SaveSystemSettings()
    {
        _savingSystem = true;
        try
        {
            // Simular salvamento
            await Task.Delay(1000);
            Snackbar.Add("Configurações do sistema salvas com sucesso!", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar configurações: {ex.Message}", Severity.Error);
        }
        finally
        {
            _savingSystem = false;
        }
    }

    public class AppearanceSettings
    {
        public string Theme { get; set; } = "light";
        public string Language { get; set; } = "pt-BR";
        public string DateFormat { get; set; } = "dd/MM/yyyy";
        public string TimeFormat { get; set; } = "24h";
    }

    public class NotificationSettings
    {
        public bool EmailNotifications { get; set; } = true;
        public bool PushNotifications { get; set; } = true;
        public bool ProjectUpdates { get; set; } = true;
        public bool UserActivity { get; set; } = false;
        public bool SystemAlerts { get; set; } = true;
    }

    public class SecuritySettings
    {
        public int SessionTimeout { get; set; } = 60;
        public bool RequirePasswordChange { get; set; } = false;
        public int PasswordExpiryDays { get; set; } = 90;
        public bool EnableAuditLog { get; set; } = true;
        public bool RequireTwoFactor { get; set; } = false;
    }

    public class SystemSettings
    {
        public int DefaultPageSize { get; set; } = 10;
        public bool EnableAutoSave { get; set; } = true;
        public int AutoSaveInterval { get; set; } = 120;
        public bool EnableDebugMode { get; set; } = false;
        public bool EnableMaintenanceMode { get; set; } = false;
    }
}
