using BOS.Plant.Modules.OpcDa.Models;

namespace BOS.Plant.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para retry automático de operações OPC
/// </summary>
public interface IOpcRetryService : IDisposable
{
    /// <summary>
    /// Evento disparado quando uma tentativa de retry é iniciada
    /// </summary>
    event EventHandler<OpcRetryEventArgs>? RetryAttemptStarted;

    /// <summary>
    /// Evento disparado quando uma tentativa de retry é concluída
    /// </summary>
    event EventHandler<OpcRetryEventArgs>? RetryAttemptCompleted;

    /// <summary>
    /// Evento disparado quando todas as tentativas de retry são esgotadas
    /// </summary>
    event EventHandler<OpcRetryEventArgs>? RetryExhausted;

    /// <summary>
    /// Executa operação com retry automático
    /// </summary>
    /// <typeparam name="T">Tipo de retorno da operação</typeparam>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="operationType">Tipo da operação OPC</param>
    /// <param name="operationId">ID único da operação (opcional)</param>
    /// <param name="customConfig">Configuração personalizada de retry (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<T> ExecuteWithRetryAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        OpcOperationType operationType,
        string? operationId = null,
        OpcRetryConfig? customConfig = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa operação sem retorno com retry
    /// </summary>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="operationType">Tipo da operação OPC</param>
    /// <param name="operationId">ID único da operação (opcional)</param>
    /// <param name="customConfig">Configuração personalizada de retry (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task ExecuteWithRetryAsync(
        Func<CancellationToken, Task> operation,
        OpcOperationType operationType,
        string? operationId = null,
        OpcRetryConfig? customConfig = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém configuração de retry para tipo de operação
    /// </summary>
    /// <param name="operationType">Tipo da operação</param>
    /// <returns>Configuração de retry</returns>
    OpcRetryConfig GetRetryConfigForOperation(OpcOperationType operationType);

    /// <summary>
    /// Obtém estatísticas de retry
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas de retry</returns>
    Task<OpcRetryStatistics> GetRetryStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista tentativas ativas de retry
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de tentativas ativas</returns>
    Task<List<OpcActiveRetryAttempt>> GetActiveRetryAttemptsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancela tentativas de retry para operação específica
    /// </summary>
    /// <param name="operationId">ID da operação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se o retry foi cancelado, false caso contrário</returns>
    Task<bool> CancelRetryAsync(string operationId, CancellationToken cancellationToken = default);
}
