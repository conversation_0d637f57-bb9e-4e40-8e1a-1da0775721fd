using BOS.Plant.Core.Common;

namespace BOS.Plant.Core.Entities;

/// <summary>
/// Token para recuperação de senha
/// </summary>
public class PasswordResetToken : TenantBaseEntity
{
    /// <summary>
    /// ID do usuário associado ao token
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Token de recuperação (hash)
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Data e hora de expiração do token
    /// </summary>
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Indica se o token foi usado
    /// </summary>
    public bool IsUsed { get; set; } = false;

    /// <summary>
    /// Data e hora em que o token foi usado
    /// </summary>
    public DateTime? UsedAt { get; set; }

    /// <summary>
    /// Endereço IP de onde foi solicitado o reset
    /// </summary>
    public string? RequestIpAddress { get; set; }

    /// <summary>
    /// User Agent de onde foi solicitado o reset
    /// </summary>
    public string? RequestUserAgent { get; set; }

    /// <summary>
    /// Endereço IP de onde foi usado o token
    /// </summary>
    public string? UsedIpAddress { get; set; }

    /// <summary>
    /// User Agent de onde foi usado o token
    /// </summary>
    public string? UsedUserAgent { get; set; }

    /// <summary>
    /// Usuário associado ao token
    /// </summary>
    public virtual ApplicationUser? User { get; set; }

    /// <summary>
    /// Verifica se o token está válido
    /// </summary>
    /// <returns>True se válido</returns>
    public bool IsValid()
    {
        return !IsUsed && 
               !IsDeleted && 
               DateTime.UtcNow <= ExpiresAt;
    }

    /// <summary>
    /// Marca o token como usado
    /// </summary>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="userAgent">User Agent</param>
    public void MarkAsUsed(string? ipAddress = null, string? userAgent = null)
    {
        IsUsed = true;
        UsedAt = DateTime.UtcNow;
        UsedIpAddress = ipAddress;
        UsedUserAgent = userAgent;
    }

    /// <summary>
    /// Cria um novo token de reset
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <param name="token">Token gerado</param>
    /// <param name="expirationHours">Horas para expiração (padrão: 24)</param>
    /// <param name="ipAddress">Endereço IP da solicitação</param>
    /// <param name="userAgent">User Agent da solicitação</param>
    /// <returns>Nova instância do token</returns>
    public static PasswordResetToken Create(
        string userId, 
        string token, 
        int expirationHours = 24,
        string? ipAddress = null,
        string? userAgent = null)
    {
        return new PasswordResetToken
        {
            UserId = userId,
            Token = token,
            ExpiresAt = DateTime.UtcNow.AddHours(expirationHours),
            RequestIpAddress = ipAddress,
            RequestUserAgent = userAgent
        };
    }
}
