namespace BOS.Plant.Infrastructure.Services.Email.Models;

/// <summary>
/// Configurações do serviço de e-mail
/// </summary>
public class EmailSettings
{
    /// <summary>
    /// Seção de configuração no appsettings.json
    /// </summary>
    public const string SectionName = "EmailSettings";

    /// <summary>
    /// Servidor SMTP
    /// </summary>
    public string SmtpServer { get; set; } = string.Empty;

    /// <summary>
    /// Porta do servidor SMTP
    /// </summary>
    public int SmtpPort { get; set; } = 587;

    /// <summary>
    /// Indica se deve usar SSL/TLS
    /// </summary>
    public bool EnableSsl { get; set; } = true;

    /// <summary>
    /// Nome de usuário para autenticação SMTP
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// Senha para autenticação SMTP
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// E-mail do remetente
    /// </summary>
    public string FromEmail { get; set; } = string.Empty;

    /// <summary>
    /// Nome do remetente
    /// </summary>
    public string FromName { get; set; } = string.Empty;

    /// <summary>
    /// E-mail para resposta (reply-to)
    /// </summary>
    public string? ReplyToEmail { get; set; }

    /// <summary>
    /// Timeout para envio em segundos
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Indica se o serviço está habilitado
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// URL base da aplicação para links nos e-mails
    /// </summary>
    public string BaseUrl { get; set; } = string.Empty;

    /// <summary>
    /// Valida se as configurações estão corretas
    /// </summary>
    /// <returns>True se válidas</returns>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(SmtpServer) &&
               SmtpPort > 0 &&
               !string.IsNullOrWhiteSpace(UserName) &&
               !string.IsNullOrWhiteSpace(Password) &&
               !string.IsNullOrWhiteSpace(FromEmail) &&
               !string.IsNullOrWhiteSpace(FromName) &&
               !string.IsNullOrWhiteSpace(BaseUrl);
    }
}
