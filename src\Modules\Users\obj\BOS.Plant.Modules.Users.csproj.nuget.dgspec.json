{"format": 1, "restore": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj": {}}, "projects": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj", "projectName": "BOS.Plant.Core", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Nanoid": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj", "projectName": "BOS.Plant.Infrastructure", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj", "projectName": "BOS.Plant.Modules.Users", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\BOS.Plant.Modules.Users.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Modules\\Users\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Core\\BOS.Plant.Core.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Infrastructure\\BOS.Plant.Infrastructure.csproj"}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj", "projectName": "BOS.Plant.Shared", "projectPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\BOS.Plant.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\DevStudy\\BOS.Plant.System-ai\\src\\Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\DevStudy\\NuGetPackages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}