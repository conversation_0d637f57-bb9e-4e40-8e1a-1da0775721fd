using BOS.Plant.Core.Common;

namespace BOS.Plant.Core.Entities;

/// <summary>
/// Entidade que representa um projeto no sistema
/// </summary>
public class Project : TenantBaseEntity
{
    /// <summary>
    /// Nome do projeto
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do projeto
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// ID do usuário proprietário do projeto
    /// </summary>
    public string OwnerId { get; set; } = string.Empty;

    /// <summary>
    /// Usuário proprietário do projeto
    /// </summary>
    public ApplicationUser? Owner { get; set; }

    /// <summary>
    /// Data de início do projeto
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Data de fim prevista do projeto
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Status do projeto
    /// </summary>
    public ProjectStatus Status { get; set; } = ProjectStatus.Planning;

    /// <summary>
    /// Prioridade do projeto
    /// </summary>
    public ProjectPriority Priority { get; set; } = ProjectPriority.Medium;

    /// <summary>
    /// Orçamento do projeto
    /// </summary>
    public decimal? Budget { get; set; }

    /// <summary>
    /// Progresso do projeto (0-100)
    /// </summary>
    public int Progress { get; set; } = 0;

    /// <summary>
    /// Tags do projeto (JSON array)
    /// </summary>
    public string? Tags { get; set; }

    /// <summary>
    /// Configurações específicas do projeto em JSON
    /// </summary>
    public string? Settings { get; set; }

    /// <summary>
    /// Indica se o projeto está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Status do projeto
/// </summary>
public enum ProjectStatus
{
    Planning = 1,
    InProgress = 2,
    OnHold = 3,
    Completed = 4,
    Cancelled = 5
}

/// <summary>
/// Prioridade do projeto
/// </summary>
public enum ProjectPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}
