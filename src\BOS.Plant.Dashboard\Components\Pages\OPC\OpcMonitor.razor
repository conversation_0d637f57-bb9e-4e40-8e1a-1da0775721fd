@page "/opc/monitor"
@using BOS.Plant.Dashboard.Services
@using BOS.Plant.Modules.OpcDa.DTOs
@inject OpcSignalRClient OpcClient
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime
@implements IAsyncDisposable

<PageTitle>Monitor OPC - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Monitor OPC DA</MudText>
        <div class="d-flex gap-2">
            <MudButton Variant="Variant.Filled" 
                      Color="@(_isConnected ? Color.Success : Color.Error)" 
                      StartIcon="@(_isConnected ? Icons.Material.Filled.CloudDone : Icons.Material.Filled.CloudOff)"
                      OnClick="ToggleConnection">
                @(_isConnected ? "Conectado" : "Desconectado")
            </MudButton>
            <MudButton Variant="Variant.Outlined" 
                      Color="Color.Primary" 
                      StartIcon="@Icons.Material.Filled.Refresh"
                      OnClick="RefreshData"
                      Disabled="!_isConnected">
                Atualizar
            </MudButton>
        </div>
    </div>

    <!-- Status da Conexão -->
    <MudCard Elevation="2" Class="mb-4">
        <MudCardContent>
            <div class="d-flex align-center gap-4">
                <MudIcon Icon="@(_connectionStatus?.Status == "Connected" ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" 
                        Color="@(_connectionStatus?.Status == "Connected" ? Color.Success : Color.Error)" 
                        Size="Size.Large" />
                <div>
                    <MudText Typo="Typo.h6">Status: @(_connectionStatus?.Status ?? "Desconhecido")</MudText>
                    <MudText Typo="Typo.body2" Color="Color.Secondary">
                        @(_connectionStatus?.Message ?? "Aguardando conexão...")
                    </MudText>
                    @if (_connectionStatus?.Timestamp != null)
                    {
                        <MudText Typo="Typo.caption">
                            Última atualização: @_connectionStatus.Timestamp.ToString("dd/MM/yyyy HH:mm:ss")
                        </MudText>
                    }
                </div>
            </div>
        </MudCardContent>
    </MudCard>

    <!-- Estatísticas -->
    @if (_statistics != null)
    {
        <MudGrid Class="mb-4">
            <MudItem xs="12" md="3">
                <MudCard Elevation="2">
                    <MudCardContent>
                        <div class="d-flex align-center gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Timer" Color="Color.Primary" />
                            <div>
                                <MudText Typo="Typo.h6">@FormatUptime(_statistics.UptimeMs)</MudText>
                                <MudText Typo="Typo.caption">Tempo Ativo</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard Elevation="2">
                    <MudCardContent>
                        <div class="d-flex align-center gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Update" Color="Color.Success" />
                            <div>
                                <MudText Typo="Typo.h6">@_statistics.TotalUpdates</MudText>
                                <MudText Typo="Typo.caption">Total de Atualizações</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard Elevation="2">
                    <MudCardContent>
                        <div class="d-flex align-center gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Speed" Color="Color.Warning" />
                            <div>
                                <MudText Typo="Typo.h6">@_statistics.UpdatesPerSecond.ToString("F1")/s</MudText>
                                <MudText Typo="Typo.caption">Taxa de Atualização</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
            <MudItem xs="12" md="3">
                <MudCard Elevation="2">
                    <MudCardContent>
                        <div class="d-flex align-center gap-2">
                            <MudIcon Icon="@Icons.Material.Filled.Error" Color="Color.Error" />
                            <div>
                                <MudText Typo="Typo.h6">@_statistics.ConnectionErrors</MudText>
                                <MudText Typo="Typo.caption">Erros de Conexão</MudText>
                            </div>
                        </div>
                    </MudCardContent>
                </MudCard>
            </MudItem>
        </MudGrid>
    }

    <!-- Dados OPC -->
    <MudCard Elevation="2">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h6">Dados OPC em Tempo Real</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudSwitch @bind-Value="_autoRefresh" 
                          Label="Auto Refresh" 
                          Color="Color.Primary" />
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            @if (_opcData.Any())
            {
                <MudTable Items="_opcData" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm"
                         Dense="true">
                    <HeaderContent>
                        <MudTh>Tag</MudTh>
                        <MudTh>Valor</MudTh>
                        <MudTh>Qualidade</MudTh>
                        <MudTh>Tipo</MudTh>
                        <MudTh>Grupo</MudTh>
                        <MudTh>Última Atualização</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Tag">
                            <MudText Typo="Typo.body2" Style="font-family: monospace;">@context.TagName</MudText>
                        </MudTd>
                        <MudTd DataLabel="Valor">
                            <MudText Typo="Typo.body1" Style="font-weight: bold;">
                                @FormatValue(context.Value)
                            </MudText>
                        </MudTd>
                        <MudTd DataLabel="Qualidade">
                            <MudChip T="string" 
                                    Color="@GetQualityColor(context.Quality)" 
                                    Size="Size.Small">
                                @context.Quality
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Tipo">
                            <MudText Typo="Typo.caption">@context.DataType</MudText>
                        </MudTd>
                        <MudTd DataLabel="Grupo">
                            <MudText Typo="Typo.caption">@context.GroupName</MudText>
                        </MudTd>
                        <MudTd DataLabel="Última Atualização">
                            <MudText Typo="Typo.caption">
                                @context.Timestamp.ToString("HH:mm:ss.fff")
                            </MudText>
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Refresh" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             OnClick="@(() => ReadItem(context.TagName))"
                                             Disabled="!_isConnected" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                             Color="Color.Secondary" 
                                             Size="Size.Small"
                                             OnClick="@(() => WriteItem(context.TagName))"
                                             Disabled="!_isConnected" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                    <NoRecordsContent>
                        <MudText Typo="Typo.body1" Align="Align.Center" Class="pa-4">
                            Nenhum dado OPC disponível
                        </MudText>
                    </NoRecordsContent>
                </MudTable>
            }
            else
            {
                <div class="d-flex justify-center align-center pa-8">
                    @if (_isConnected)
                    {
                        <div class="text-center">
                            <MudProgressCircular Indeterminate="true" />
                            <MudText Typo="Typo.body1" Class="mt-2">Aguardando dados OPC...</MudText>
                        </div>
                    }
                    else
                    {
                        <MudText Typo="Typo.body1" Color="Color.Secondary">
                            Conecte-se ao serviço OPC para visualizar os dados
                        </MudText>
                    }
                </div>
            }
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private bool _isConnected = false;
    private bool _autoRefresh = true;
    private OpcConnectionStatusDto? _connectionStatus;
    private OpcServerStatisticsDto? _statistics;
    private List<OpcDataUpdateDto> _opcData = new();
    private Timer? _refreshTimer;

    protected override async Task OnInitializedAsync()
    {
        // Configurar eventos do cliente SignalR
        OpcClient.DataReceived += OnDataReceived;
        OpcClient.ConnectionStatusChanged += OnConnectionStatusChanged;
        OpcClient.StatisticsReceived += OnStatisticsReceived;
        OpcClient.AllItemsReceived += OnAllItemsReceived;
        OpcClient.ErrorReceived += OnErrorReceived;

        // Tentar conectar
        await ConnectToOpcService();

        // Configurar timer para refresh automático das estatísticas
        _refreshTimer = new Timer(async _ =>
        {
            if (_autoRefresh && _isConnected)
            {
                await InvokeAsync(async () =>
                {
                    await OpcClient.RequestStatisticsAsync();
                });
            }
        }, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    private async Task ConnectToOpcService()
    {
        try
        {
            await OpcClient.ConnectAsync();
            _isConnected = OpcClient.IsConnected;
            
            if (_isConnected)
            {
                Snackbar.Add("Conectado ao serviço OPC", Severity.Success);
                await OpcClient.RequestAllItemsAsync();
                await OpcClient.RequestStatisticsAsync();
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao conectar: {ex.Message}", Severity.Error);
        }
        
        StateHasChanged();
    }

    private async Task ToggleConnection()
    {
        if (_isConnected)
        {
            await OpcClient.DisconnectAsync();
            _isConnected = false;
            Snackbar.Add("Desconectado do serviço OPC", Severity.Info);
        }
        else
        {
            await ConnectToOpcService();
        }
    }

    private async Task RefreshData()
    {
        if (_isConnected)
        {
            await OpcClient.RequestAllItemsAsync();
            await OpcClient.RequestStatisticsAsync();
        }
    }

    private async Task ReadItem(string tagName)
    {
        if (_isConnected)
        {
            await OpcClient.RequestItemReadAsync(tagName);
        }
    }

    private async Task WriteItem(string tagName)
    {
        // Implementar dialog para escrita de valor
        // Por simplicidade, vamos apenas mostrar uma mensagem
        Snackbar.Add($"Funcionalidade de escrita para {tagName} será implementada", Severity.Info);
    }

    private void OnDataReceived(object? sender, OpcDataUpdateDto data)
    {
        InvokeAsync(() =>
        {
            var existingItem = _opcData.FirstOrDefault(x => x.TagName == data.TagName);
            if (existingItem != null)
            {
                existingItem.Value = data.Value;
                existingItem.Quality = data.Quality;
                existingItem.Timestamp = data.Timestamp;
                existingItem.DataType = data.DataType;
            }
            else
            {
                _opcData.Add(data);
            }
            StateHasChanged();
        });
    }

    private void OnConnectionStatusChanged(object? sender, OpcConnectionStatusDto status)
    {
        InvokeAsync(() =>
        {
            _connectionStatus = status;
            StateHasChanged();
        });
    }

    private void OnStatisticsReceived(object? sender, OpcServerStatisticsDto statistics)
    {
        InvokeAsync(() =>
        {
            _statistics = statistics;
            StateHasChanged();
        });
    }

    private void OnAllItemsReceived(object? sender, List<OpcDataUpdateDto> items)
    {
        InvokeAsync(() =>
        {
            _opcData = items.ToList();
            StateHasChanged();
        });
    }

    private void OnErrorReceived(object? sender, string error)
    {
        InvokeAsync(() =>
        {
            Snackbar.Add(error, Severity.Error);
        });
    }

    private string FormatValue(object? value)
    {
        if (value == null) return "null";
        
        return value switch
        {
            double d => d.ToString("F2"),
            float f => f.ToString("F2"),
            decimal dec => dec.ToString("F2"),
            bool b => b ? "True" : "False",
            _ => value.ToString() ?? "null"
        };
    }

    private Color GetQualityColor(string quality)
    {
        return quality switch
        {
            "Good" => Color.Success,
            "Uncertain" => Color.Warning,
            "Bad" => Color.Error,
            _ => Color.Default
        };
    }

    private string FormatUptime(long uptimeMs)
    {
        var timespan = TimeSpan.FromMilliseconds(uptimeMs);
        if (timespan.TotalDays >= 1)
            return $"{timespan.Days}d {timespan.Hours}h {timespan.Minutes}m";
        if (timespan.TotalHours >= 1)
            return $"{timespan.Hours}h {timespan.Minutes}m";
        return $"{timespan.Minutes}m {timespan.Seconds}s";
    }

    public async ValueTask DisposeAsync()
    {
        _refreshTimer?.Dispose();
        
        OpcClient.DataReceived -= OnDataReceived;
        OpcClient.ConnectionStatusChanged -= OnConnectionStatusChanged;
        OpcClient.StatisticsReceived -= OnStatisticsReceived;
        OpcClient.AllItemsReceived -= OnAllItemsReceived;
        OpcClient.ErrorReceived -= OnErrorReceived;
        
        await OpcClient.DisposeAsync();
    }
}
