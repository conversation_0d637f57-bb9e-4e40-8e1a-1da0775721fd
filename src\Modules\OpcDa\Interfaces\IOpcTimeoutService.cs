using BOS.Plant.Modules.OpcDa.Models;

namespace BOS.Plant.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para gerenciamento de timeouts em operações OPC
/// </summary>
public interface IOpcTimeoutService : IDisposable
{
    /// <summary>
    /// Evento disparado quando uma operação expira
    /// </summary>
    event EventHandler<OpcTimeoutEventArgs>? OperationTimedOut;

    /// <summary>
    /// Evento disparado quando uma operação é concluída
    /// </summary>
    event EventHandler<OpcTimeoutEventArgs>? OperationCompleted;

    /// <summary>
    /// Executa operação com timeout configurável
    /// </summary>
    /// <typeparam name="T">Tipo de retorno da operação</typeparam>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="operationType">Tipo da operação OPC</param>
    /// <param name="operationId">ID único da operação (opcional)</param>
    /// <param name="customTimeout">Timeout personalizado (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Resultado da operação</returns>
    Task<T> ExecuteWithTimeoutAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        OpcOperationType operationType,
        string? operationId = null,
        TimeSpan? customTimeout = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Executa operação sem retorno com timeout
    /// </summary>
    /// <param name="operation">Operação a ser executada</param>
    /// <param name="operationType">Tipo da operação OPC</param>
    /// <param name="operationId">ID único da operação (opcional)</param>
    /// <param name="customTimeout">Timeout personalizado (opcional)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task ExecuteWithTimeoutAsync(
        Func<CancellationToken, Task> operation,
        OpcOperationType operationType,
        string? operationId = null,
        TimeSpan? customTimeout = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém timeout configurado para tipo de operação
    /// </summary>
    /// <param name="operationType">Tipo da operação</param>
    /// <returns>Timeout configurado</returns>
    TimeSpan GetTimeoutForOperation(OpcOperationType operationType);

    /// <summary>
    /// Define timeout personalizado para tipo de operação
    /// </summary>
    /// <param name="operationType">Tipo da operação</param>
    /// <param name="timeout">Novo timeout</param>
    void SetTimeoutForOperation(OpcOperationType operationType, TimeSpan timeout);

    /// <summary>
    /// Obtém estatísticas de timeout
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas de timeout</returns>
    Task<OpcTimeoutStatistics> GetTimeoutStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista operações ativas
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de operações ativas</returns>
    Task<List<OpcActiveOperation>> GetActiveOperationsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancela operação específica
    /// </summary>
    /// <param name="operationId">ID da operação</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se a operação foi cancelada, false caso contrário</returns>
    Task<bool> CancelOperationAsync(string operationId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancela todas as operações ativas
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task CancelAllOperationsAsync(CancellationToken cancellationToken = default);
}
